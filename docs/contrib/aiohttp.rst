.. _contrib_aiohttp:

================================
Tortoise-ORM aiohttp integration
================================

We have a lightweight integration util ``tortoise.contrib.aiohttp`` which has a single function ``register_tortoise`` which sets up Tortoise-ORM on startup and cleans up on teardown.

See the :ref:`example_aiohttp`

Reference
=========

.. automodule:: tortoise.contrib.aiohttp
    :members:
    :undoc-members:
    :show-inheritance:
