import logging
import os
from datetime import datetime, timezone, timedelta

from tortoise import BaseDBAsyncClient
from tortoise.transactions import in_transaction

from mygpt.models import Plan, DurationUnit, User, PlanSubscription, SubscriptionStatus

IS_USE_LOCAL_VLLM = os.getenv("IS_USE_LOCAL_VLLM", "false").lower() == "true"

print(f"IS_USE_LOCAL_VLLM: {IS_USE_LOCAL_VLLM}")

TRIAL_PLAN_DEFAULTS = {
    "id": "00000001-f0a9-46e4-b866-a8d9cd82fcb2",
    "name": "Trial Plan",
    "plan_type": "trial",
    "price": 0,
    "description": "Default 14-day trial plan for new users.",
    "message_quota": 100,
    "storage_quota": 50 * 1024 * 1024,  # 50MB in bytes
    "bot_quota": 3,
    "web_page_quota": 100,
    "multi_modal_parsing_quota": 10,
}


async def upgrade(db: BaseDBAsyncClient) -> str:
    """创建内置试用计划并为符合条件的用户自动订阅"""
    if IS_USE_LOCAL_VLLM:
        print("Skipping trial plan migration for on-prem.")
        return "SELECT 1 WHERE FALSE;"
    async with in_transaction() as connection:
        plan = None
        # 使用事务锁确保并发安全
        try:
            plan = (
                await Plan.filter(id="00000001-f0a9-46e4-b866-a8d9cd82fcb2")
                .select_for_update()
                .first()
            )
        except Exception as e:
            logging.error(f"Error while get plan: {e}, ignore")
        if plan:
            print(f"Built-in Trial Plan {plan} already exists.")
        else:
            plan = await Plan.create(**TRIAL_PLAN_DEFAULTS)
            print(f"Built-in Trial Plan created with ID: {plan.id}")

        # 为符合条件的用户自动订阅 trial 套餐
        # 1. 查找所有没有 corporate 属性的用户
        users = await User.filter(corporate=False, deleted_at__isnull=True).all()

        print(f"Found {len(users)} users without corporate attribute")

        for user in users:
            # 检查用户是否已经有活跃的订阅
            active_subscription = await user.get_all_plan_subscriptions()
            if active_subscription:
                # 如果有历史订阅，就略过
                logging.info(
                    f"User {user.id} already has an active subscription, skipping."
                )
                continue
            # 创建 trial 套餐订阅
            start_at = datetime.now(timezone.utc)
            expires_at = start_at + timedelta(days=14)  # 14 天有效期

            await PlanSubscription.create(
                user_id=user.id,
                plan_id=plan.id,
                start_at=start_at,
                expires_at=expires_at,
                status=SubscriptionStatus.ACTIVE,
            )
        print(f"Total subscribed users: {len(users)}")

    return "SELECT 1 WHERE FALSE;"


async def downgrade(db: BaseDBAsyncClient) -> str:
    """
    Removes the built-in Trial Plan from the database.
    (Use with caution, usually not needed unless retracting the feature).
    """
    # Find and delete the specific trial plan
    to_be_deleted = await Plan.get_or_none(id="00000001-f0a9-46e4-b866-a8d9cd82fcb2")
    if to_be_deleted:
        await to_be_deleted.delete()
        print(f"Deleted [{to_be_deleted}] Trial Plan")
    else:
        print("No Trial Plan record found to delete.")
    return "SELECT 1 WHERE FALSE;"
