FROM python:3.10-slim-buster

LABEL maintainer="<EMAIL>"

ENV TZ=Asia/Tokyo
ENV DEBUG=0
ENV TIKTOKEN_CACHE_DIR=/backend/data-gym-cach

RUN apt-get update && \
    apt-get -y install gcc libpq-dev tzdata git wget

COPY . /backend
WORKDIR /backend

# 安装Poetry
RUN pip install poetry
RUN poetry self add poetry-plugin-export

# 使用Poetry生成requirements.txt
RUN poetry export -f requirements.txt --output requirements.txt --without-hashes

# Use system pip
RUN pip install  --isolated  -r requirements.txt
RUN pip install pip_system_certs
RUN pip install zstandard aioredis psutil ijson aiofiles matplotlib

# soffice
RUN apt-get update
RUN apt-get -y install libreoffice fontconfig fonts-wqy-zenhei vim
RUN fc-cache -fv
RUN wget https://ftp-srv2.kddilabs.jp/office/tdf/libreoffice/stable/25.2.0/deb/x86_64/LibreOffice_25.2.0_Linux_x86-64_deb.tar.gz
RUN tar -xvzf LibreOffice_25.2.0_Linux_x86-64_deb.tar.gz
RUN dpkg -i LibreOffice_25.2.0.3_Linux_x86-64_deb/DEBS/*.deb
RUN rm -rf LibreOffice_25.2.0.3_Linux_x86-64_deb
RUN rm LibreOffice_25.2.0_Linux_x86-64_deb.tar.gz
RUN rm /usr/bin/soffice
RUN ln -s /opt/libreoffice25.2/program/soffice /usr/bin/soffice

EXPOSE 8000

RUN chmod +x ./startup.sh
CMD ["/bin/sh", "-c", "./startup.sh"]