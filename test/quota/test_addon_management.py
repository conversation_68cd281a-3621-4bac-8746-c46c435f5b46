import unittest
from datetime import datetime, timezone, timedelta
from unittest.mock import patch, MagicMock, AsyncMock
from uuid import UUID

import pytest

from mygpt.models import (
    User,
    AddOn,
    AddOnSubscription,
    DurationUnit,
)
from mygpt.enums import UsageType


class TestAddonManagement:
    """Test the add-on management functionality."""

    def setup_method(self, method):
        """Set up test data."""
        # Create a test user
        self.user_id = UUID("12345678-1234-5678-1234-************")
        self.user = MagicMock(spec=User)
        self.user.id = self.user_id
        self.user.user_id = self.user_id
        self.user.corporate = False

        # Create test add-ons
        self.message_addon = MagicMock(spec=AddOn)
        self.message_addon.id = UUID("aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa")
        self.message_addon.name = "Message Add-On"
        self.message_addon.target_field = "message_quota"
        self.message_addon.value = 500
        self.message_addon.duration_unit = DurationUnit.MONTH
        self.message_addon.duration_length = 1

        self.bot_addon = MagicMock(spec=AddOn)
        self.bot_addon.id = UUID("bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb")
        self.bot_addon.name = "Bot Add-On"
        self.bot_addon.target_field = "bot_quota"
        self.bot_addon.value = 5
        self.bot_addon.duration_unit = DurationUnit.MONTH
        self.bot_addon.duration_length = 1

        self.webpage_addon = MagicMock(spec=AddOn)
        self.webpage_addon.id = UUID("cccccccc-cccc-cccc-cccc-cccccccccccc")
        self.webpage_addon.name = "Webpage Add-On"
        self.webpage_addon.target_field = "web_page_quota"
        self.webpage_addon.value = 200
        self.webpage_addon.duration_unit = DurationUnit.MONTH
        self.webpage_addon.duration_length = 1

        self.multimodal_addon = MagicMock(spec=AddOn)
        self.multimodal_addon.id = UUID("dddddddd-dddd-dddd-dddd-dddddddddddd")
        self.multimodal_addon.name = "Multimodal Add-On"
        self.multimodal_addon.target_field = "multi_modal_parsing_quota"
        self.multimodal_addon.value = 20
        self.multimodal_addon.duration_unit = DurationUnit.MONTH
        self.multimodal_addon.duration_length = 1

        # Create test add-on subscriptions
        self.message_addon_subscription = MagicMock(spec=AddOnSubscription)
        self.message_addon_subscription.id = UUID(
            "11111111-1111-1111-1111-111111111111"
        )
        self.message_addon_subscription.user_id = self.user_id
        self.message_addon_subscription.add_on_id = self.message_addon.id
        self.message_addon_subscription.add_on = self.message_addon
        self.message_addon_subscription.is_cancelled = False
        self.message_addon_subscription.start_at = datetime.now(timezone.utc)
        self.message_addon_subscription.expires_at = (
            self.message_addon_subscription.start_at + timedelta(days=30)
        )

    @pytest.mark.asyncio
    @patch("mygpt.services.addon_service.AddOn.get")
    @patch("mygpt.services.addon_service.AddOnSubscription.create")
    async def test_subscribe_addon(self, mock_create_subscription, mock_get_addon):
        """Test subscribing to an add-on."""
        from mygpt.services.addon_service import subscribe_addon

        # Setup mocks
        mock_get_addon.return_value = self.message_addon
        mock_create_subscription.return_value = self.message_addon_subscription

        # Call the method
        subscription = await subscribe_addon(self.user_id, self.message_addon.id)

        # Verify the result
        assert subscription is not None
        assert subscription.user_id == self.user_id
        assert subscription.add_on_id == self.message_addon.id
        assert subscription.is_cancelled is False

        # Verify that the subscription start and end times are calculated correctly
        assert (subscription.expires_at - subscription.start_at).days == 30

        # Verify that the mocks were called correctly
        mock_get_addon.assert_called_once_with(id=self.message_addon.id)
        mock_create_subscription.assert_called_once()

    @pytest.mark.asyncio
    @patch("mygpt.services.addon_service.AddOn.get")
    @patch("mygpt.services.addon_service.AddOnSubscription.create")
    async def test_subscribe_multiple_addons(
        self, mock_create_subscription, mock_get_addon
    ):
        """Test subscribing to multiple add-ons."""
        from mygpt.services.addon_service import subscribe_addon

        # Setup mocks for first add-on
        mock_get_addon.side_effect = [self.message_addon, self.bot_addon]

        # Create a bot add-on subscription
        bot_addon_subscription = MagicMock(spec=AddOnSubscription)
        bot_addon_subscription.id = UUID("*************-2222-2222-************")
        bot_addon_subscription.user_id = self.user_id
        bot_addon_subscription.add_on_id = self.bot_addon.id
        bot_addon_subscription.add_on = self.bot_addon
        bot_addon_subscription.is_cancelled = False
        bot_addon_subscription.start_at = datetime.now(timezone.utc)
        bot_addon_subscription.expires_at = bot_addon_subscription.start_at + timedelta(
            days=30
        )

        mock_create_subscription.side_effect = [
            self.message_addon_subscription,
            bot_addon_subscription,
        ]

        # Subscribe to first add-on
        subscription1 = await subscribe_addon(self.user_id, self.message_addon.id)

        # Subscribe to second add-on
        subscription2 = await subscribe_addon(self.user_id, self.bot_addon.id)

        # Verify the results
        assert subscription1 is not None
        assert subscription1.user_id == self.user_id
        assert subscription1.add_on_id == self.message_addon.id

        assert subscription2 is not None
        assert subscription2.user_id == self.user_id
        assert subscription2.add_on_id == self.bot_addon.id

        # Verify that both subscriptions are active
        assert subscription1.is_cancelled is False
        assert subscription2.is_cancelled is False

        # Verify that the mocks were called correctly
        assert mock_get_addon.call_count == 2
        assert mock_create_subscription.call_count == 2

    @pytest.mark.asyncio
    @patch("mygpt.services.addon_service.AddOn.get")
    @patch("mygpt.services.addon_service.AddOnSubscription.create")
    async def test_calculate_addon_subscription_dates(
        self, mock_create_subscription, mock_get_addon
    ):
        """Test that add-on subscription dates are calculated correctly based on add-on duration."""
        from mygpt.services.addon_service import subscribe_addon

        # Create add-ons with different durations
        daily_addon = MagicMock(spec=AddOn)
        daily_addon.id = UUID("eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee")
        daily_addon.name = "Daily Add-On"
        daily_addon.target_field = "message_quota"
        daily_addon.value = 100
        daily_addon.duration_unit = DurationUnit.DAY
        daily_addon.duration_length = 7  # 7 days

        monthly_addon = MagicMock(spec=AddOn)
        monthly_addon.id = UUID("ffffffff-ffff-ffff-ffff-ffffffffffff")
        monthly_addon.name = "Monthly Add-On"
        monthly_addon.target_field = "message_quota"
        monthly_addon.value = 500
        monthly_addon.duration_unit = DurationUnit.MONTH
        monthly_addon.duration_length = 3  # 3 months

        # Setup mocks
        mock_get_addon.side_effect = [daily_addon, monthly_addon]

        # Create subscriptions with different durations
        start_time = datetime.now(timezone.utc)

        daily_subscription = MagicMock(spec=AddOnSubscription)
        daily_subscription.id = UUID("*************-3333-3333-************")
        daily_subscription.user_id = self.user_id
        daily_subscription.add_on_id = daily_addon.id
        daily_subscription.add_on = daily_addon
        daily_subscription.is_cancelled = False
        daily_subscription.start_at = start_time
        daily_subscription.expires_at = start_time + timedelta(days=7)

        monthly_subscription = MagicMock(spec=AddOnSubscription)
        monthly_subscription.id = UUID("*************-4444-4444-************")
        monthly_subscription.user_id = self.user_id
        monthly_subscription.add_on_id = monthly_addon.id
        monthly_subscription.add_on = monthly_addon
        monthly_subscription.is_cancelled = False
        monthly_subscription.start_at = start_time
        monthly_subscription.expires_at = start_time + timedelta(
            days=90
        )  # Approximately 3 months

        mock_create_subscription.side_effect = [
            daily_subscription,
            monthly_subscription,
        ]

        # Subscribe to daily add-on
        daily_sub = await subscribe_addon(self.user_id, daily_addon.id)

        # Subscribe to monthly add-on
        monthly_sub = await subscribe_addon(self.user_id, monthly_addon.id)

        # Verify the results
        assert daily_sub is not None
        assert (daily_sub.expires_at - daily_sub.start_at).days == 7

        assert monthly_sub is not None
        assert (monthly_sub.expires_at - monthly_sub.start_at).days == 90

        # Verify that the mocks were called correctly
        assert mock_get_addon.call_count == 2
        assert mock_create_subscription.call_count == 2

    @pytest.mark.asyncio
    @patch("mygpt.services.addon_service.AddOnSubscription.filter")
    @patch("mygpt.services.addon_service.AddOnSubscription.get")
    async def test_cancel_addon_subscription(
        self, mock_get_subscription, mock_filter_subscription
    ):
        """Test cancelling an add-on subscription."""
        from mygpt.services.addon_service import cancel_addon_subscription

        # Setup mocks
        mock_get_subscription.return_value = self.message_addon_subscription

        # Call the method
        result = await cancel_addon_subscription(
            self.user_id, self.message_addon_subscription.id
        )

        # Verify the result
        assert result is True
        assert self.message_addon_subscription.is_cancelled is True
        assert self.message_addon_subscription.cancelled_at is not None

        # Verify that the mocks were called correctly
        mock_get_subscription.assert_called_once_with(
            id=self.message_addon_subscription.id, user_id=self.user_id
        )


if __name__ == "__main__":
    unittest.main()
