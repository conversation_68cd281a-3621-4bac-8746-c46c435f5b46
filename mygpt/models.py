import base64
import hashlib
import json
import logging
import os
import traceback
import uuid
from datetime import datetime, timezone, timedelta
from enum import Enum
from typing import Any, List, Optional, Tuple

from cryptography.hazmat.backends import default_backend
from cryptography.hazmat.primitives import serialization
from cryptography.hazmat.primitives.asymmetric import rsa
from dateutil.relativedelta import relativedelta
from fastapi.encoders import jsonable_encoder
from pydantic import BaseModel
from tortoise import fields, models
from tortoise.exceptions import MultipleObjectsReturned
from tortoise.validators import MinValueValidator

from mygpt.aes_util import AESCipher
from mygpt.enums import (
    DATASET_STATUS,
    EMBEDDINGS_MODEL,
    FAQ_TYPE,
    PROMPT_TYPE,
    AIConfigType,
    AIModel,
    AIStatus,
    AIType,
    ExcelTestTaskStatus,
    FaqSourceType,
    FileSource,
    FileType,
    OpenAIModel,
    OpenaiApikeyStatus,
    QuestionType,
    ResourceType,
    SessionMessageStatus,
    StripeModel,
    VectorFileStatus,
    VectorFileType,
    VectorStatus,
    VectorTextType,
    UserConfigType,
    MESSAGE_COMES_FROM,
    MessagePageInfo,
    FileStorage,
    RobotAccessType,
    RobotType,
    VectorFileSourceType,
    IntegrationRuleType,
    IntegrationRuleFileListSyncingStatus,
    IntegrationSyncOperation,
    IntegrationSyncTriggerType,
)
from mygpt.error import sentry_sdk_capture_exception
from mygpt.prompt import (
    digital_human_default_prompt,
    get_unknown_text,
    digital_human_robot_default_prompt,
)
from mygpt.pydantic_rules import LarkIntegrationRuleInput
from mygpt.settings import (
    AES_SECRETS,
    AWS_S3_BUCKET_NAME,
    VECTOR_FILE_DEFAULT_LEARN_TYPE,
)


class UsageType(str, Enum):
    """Usage Type Enum"""

    # Consumable types tracked by QuotaService
    MESSAGE_SENT = "message_sent"  # 消息发送数量
    WEB_PAGE_LEARNED = "web_page_learned"  # 网页学习数量
    MULTI_MODAL_PARSING = "multi_modal_parsing"  # 多模态解析数量

    # Types related to concurrent limits
    BOT_CREATED = "bot_created"  # 机器人创建数量


class AbstractBaseModel(models.Model):
    """
    Abstract Base Model
    """

    id = fields.UUIDField(pk=True)
    created_at = fields.DatetimeField(auto_now_add=True)
    updated_at = fields.DatetimeField(auto_now=True)

    async def save(self, update_updated_at=True, *args, **kwargs):
        if not update_updated_at:
            self._meta.fields_map["updated_at"].auto_now = False

        await super().save(*args, **kwargs)

        if not update_updated_at:
            self._meta.fields_map["updated_at"].auto_now = True

    class Meta:
        abstract = True


class AbstractBaseModelWithDeletedAt(AbstractBaseModel):
    """
    Abstract Base Model With Deleted At
    """

    deleted_at = fields.DatetimeField(null=True)

    class Meta:
        abstract = True

    async def soft_delete(self):
        self.deleted_at = datetime.now()
        await self.save(update_fields=["deleted_at"])


class Md5Info(BaseModel):
    index_id: str
    md5: str
    vector_file_ids: list[str]


class EmbeddingParams(BaseModel):
    provider: EMBEDDINGS_MODEL
    model_name: OpenAIModel
    dimensions: Optional[int] = None

    def __eq__(self, other: "EmbeddingParams"):
        if not isinstance(other, EmbeddingParams):
            raise ValueError("The other object is not an instance of EmbeddingParams")
        return (
            self.provider == other.provider
            and self.model_name == other.model_name
            and self.dimensions == other.dimensions
        )


class User(AbstractBaseModelWithDeletedAt):
    user_id = fields.CharField(max_length=100, unique=True)
    email = fields.CharField(max_length=100)
    name = fields.CharField(max_length=100)
    password = fields.CharField(max_length=64, null=True)
    salt = fields.CharField(max_length=64, null=True)
    picture = fields.CharField(max_length=1500, null=True)
    locale = fields.CharField(max_length=10, null=True)
    corporate = fields.BooleanField(default=False)
    # 是否是独立站点的用户
    is_independent = fields.BooleanField(default=False)

    openai_apikeys: fields.ReverseRelation["OpenaiApikey"]
    robots: fields.ReverseRelation["Robot"]
    questionrecords: fields.ReverseRelation["QuestionRecord"]
    sessionmessages: fields.ReverseRelation["SessionMessage"]
    sessionusers: fields.ReverseRelation["SessionUser"]
    user_excel_test_tasks: fields.ReverseRelation["ExcelTestTask"]
    datasets: fields.ReverseRelation["Dataset"]
    inside_preview_users: fields.ReverseRelation["InsiderPreviewUser"]
    userconfigs: fields.ReverseRelation["UserConfig"]
    agent_function_call_api: fields.ReverseRelation["AgentFunctionCallApi"]
    integration_sync_logs: fields.ReverseRelation["IntegrationSyncLog"]
    created_integration_rules: fields.ReverseRelation["IntegrationRule"]

    plan_subscriptions: fields.ReverseRelation["PlanSubscription"]
    add_on_subscriptions: fields.ReverseRelation["AddOnSubscription"]
    usage_logs: fields.ReverseRelation["UsageLog"]

    @classmethod
    def generate_salt(cls):
        return os.urandom(16).hex()

    @classmethod
    def hash_password(cls, password, salt):
        hash_input = f"{password}{salt}".encode("utf-8")
        hashed_password = hashlib.sha256(hash_input).hexdigest()
        return hashed_password

    @classmethod
    async def store_password_and_salt(cls, id, password):
        salt = User.generate_salt()
        hashed_password = User.hash_password(password, salt)
        await cls.filter(id=id).update(password=hashed_password, salt=salt)
        return salt, hashed_password

    @classmethod
    async def verify_password(cls, id, input_password):
        # 从数据库获取 salt 和 hashed_password
        user = await User.get_or_none(id=id)
        if not user:
            return False

        # 测试使用
        # if not user.password:
        #     salt, hashed_password = await User.store_password_and_salt(user.id, '123456')
        #     user.salt = salt
        #     user.password = hashed_password

        salt = user.salt
        hashed_password_from_db = user.password
        # 计算输入密码的哈希值
        hashed_input_password = User.hash_password(input_password, salt)
        return hashed_input_password == hashed_password_from_db

    @classmethod
    async def is_super_admin(cls, user_id):
        user_obj = await cls.get_or_none(
            user_id=user_id,
            deleted_at__isnull=True,
        ).prefetch_related("userconfigs")
        if not user_obj:
            return False
        for config in user_obj.userconfigs:
            if (
                config.key == UserConfigType.SUPER_ADMIN
                and config.value.lower() == "true"
            ):
                return True
        return False

    async def get_active_plan_subscription(self) -> Optional["PlanSubscription"]:
        """
        获取用户当前最新的有效订阅，基于时间范围和状态判断
        get active plan 也用这个函数
        """
        now = datetime.now(timezone.utc)
        # Find subscriptions that are currently valid based on time, cancellation and deletion status
        active_subscription = (
            await PlanSubscription.filter(
                user_id=self.id,
                is_cancelled=False,
                start_at__lte=now,
                expires_at__gt=now,
                deleted_at__isnull=True,
            )
            .order_by("-created_at")
            .prefetch_related("plan")
            .first()  #  一个用户最多只有一个 active plan
        )
        return active_subscription

    async def get_all_plan_subscriptions(self) -> List["PlanSubscription"]:
        """获取用户所有的订阅，不分页，数量应该不会太多"""
        subscriptions = (
            await PlanSubscription.filter(
                user_id=self.id,
                deleted_at__isnull=True,
            )
            .prefetch_related("plan")
            .order_by("-created_at")
            .all()
        )
        return subscriptions

    async def has_any_plan_subscription_history(self) -> bool:
        """检查用户是否有任何订阅历史（包括已取消或已过期的）"""
        has_history = await PlanSubscription.filter(
            user_id=self.id,
            deleted_at__isnull=True,
        ).exists()
        return has_history

    async def get_active_add_on_subscription(self) -> list["AddOnSubscription"]:
        """获取用户当前最新的有效附加功能订阅，可选择按目标字段类型过滤"""
        now = datetime.now(timezone.utc)
        active_subscription = (
            await AddOnSubscription.filter(
                user_id=self.id,
                is_cancelled=False,
                start_at__lte=now,
                expires_at__gt=now,
                deleted_at__isnull=True,
            )
            .prefetch_related("add_on")
            .order_by("-created_at")
            .all()
        )

        return active_subscription

    async def get_all_add_on_subscriptions(self) -> List["AddOnSubscription"]:
        """获取用户所有的活跃附加功能订阅，可选择按目标字段类型过滤"""
        active_subscriptions = await AddOnSubscription.filter(
            user_id=self.id,
            deleted_at__isnull=True,
        ).prefetch_related("add_on")
        return active_subscriptions

    async def check_quota(self, usage_type: UsageType, amount: int = 1) -> bool:
        """检查用户是否有足够的配额"""
        from mygpt.services.quota_service import QuotaService

        return await QuotaService.check_quota(str(self.id), usage_type, amount)

    async def consume_quota(self, usage_type: UsageType, amount: int = 1) -> None:
        """消费用户的配额"""
        from mygpt.services.quota_service import QuotaService

        await QuotaService.consume_quota(str(self.id), usage_type, amount)

    async def check_bot_quota(self) -> bool:
        """检查用户是否可以创建新的机器人"""
        from mygpt.services.quota_service import QuotaService

        return await QuotaService.check_bot_quota(str(self.id))

    async def get_bot_quota_details(self) -> Tuple[int, Optional[int]]:
        """获取用户的机器人配额详情"""
        from mygpt.services.quota_service import QuotaService

        return await QuotaService.get_bot_quota_details(str(self.id))

    class PydanticMeta:
        exclude = (
            "created_at",
            "updated_at",
            "deleted_at",
            "robots",
            "openai_apikeys",
            "questionrecords",
            "user_stripe_payments",
            "user_faqs",
            "sessionmessages",
            "sessionusers",
            "user_excel_test_tasks",
            "datasets",
            "inside_preview_users",
            "password",
            "member",
            "account",
            "agent_function_call_api",
            "integration_sync_logs",
            "created_integration_rules",
            "plan_subscriptions",
            "add_on_subscriptions",
            "usage_logs",
        )


class UserConfig(AbstractBaseModel):
    user: fields.ForeignKeyRelation[User] = fields.ForeignKeyField(
        "models.User",
        related_name="userconfigs",
        to_field="user_id",
    )
    key = fields.CharEnumField(UserConfigType, max_length=100)
    value = fields.CharField(max_length=255)

    @classmethod
    async def set_config(
        cls, user_id, key, value, using_db: Optional[models.BaseDBAsyncClient] = None
    ):
        return await cls.update_or_create(
            using_db=using_db,
            user_id=user_id,
            key=key,
            defaults={"value": value},
        )

    @classmethod
    async def set_configs(cls, user_id, configs: dict):
        for key, value in configs.items():
            await cls.set_config(user_id, key, value)

    @classmethod
    async def get_config(cls, user_id, key: UserConfigType):
        config_obj = await cls.get_or_none(user_id=user_id, key=key)
        return config_obj.value if config_obj else None

    class PydanticMeta:
        exclude = (
            "created_at",
            "updated_at",
            "user",
            "user_id",
            "id",
        )


class Auth0Key(AbstractBaseModelWithDeletedAt):
    public_key = fields.TextField()
    private_key = fields.TextField()

    @classmethod
    def create_pair(cls):
        # 生成密钥对
        private_key = rsa.generate_private_key(
            public_exponent=65537, key_size=2048, backend=default_backend()
        )

        public_key = private_key.public_key()

        # 编码密钥为字符串
        private_pem = private_key.private_bytes(
            encoding=serialization.Encoding.PEM,
            format=serialization.PrivateFormat.PKCS8,
            encryption_algorithm=serialization.NoEncryption(),
        )

        public_pem = public_key.public_bytes(
            encoding=serialization.Encoding.PEM,
            format=serialization.PublicFormat.SubjectPublicKeyInfo,
        )

        private_key_str = base64.b64encode(private_pem).decode("utf-8")
        public_key_str = base64.b64encode(public_pem).decode("utf-8")
        return private_key_str, public_key_str


class Robot(AbstractBaseModelWithDeletedAt):
    name = fields.CharField(
        max_length=100,
    )
    discrible = fields.TextField(null=True)
    # 专题名称,对一个组织来说，一般为组织名称，对文件来说，可以是文件名称
    subject_name = fields.TextField(null=True)
    prompt = fields.TextField(null=True)
    # 机器人类型, RAG / 数字人 / Agent
    robot_type = fields.CharEnumField(RobotType, max_length=100, default=RobotType.RAG)
    ai_type = fields.CharEnumField(AIType, max_length=20, default=AIType.PRIVATE)
    ai_model = fields.CharEnumField(AIModel, max_length=20, default=AIModel.FILE)
    ai_status = fields.CharEnumField(AIStatus, max_length=20, default=AIStatus.READY)

    robotconfigs: fields.ReverseRelation["RobotConfig"]
    questions: fields.ReverseRelation["Question"]
    urls: fields.ReverseRelation["Url"]
    user: fields.ForeignKeyRelation["User"] = fields.ForeignKeyField(
        "models.User",
        related_name="robots",
        to_field="user_id",
    )
    vectorfiles: fields.ReverseRelation["VectorFile"]
    questionrecords: fields.ReverseRelation["QuestionRecord"]
    faqs: fields.ReverseRelation["Faqs"]
    robotmd5s: fields.ReverseRelation["RobotMD5"]
    sessions: fields.ReverseRelation["Session"]
    image_attachments: fields.ReverseRelation["ImageAttachment"]
    faq_property: fields.ReverseRelation["FaqProperty"]
    function_call_api: fields.ReverseRelation["FunctionCallApi"]
    datasets: fields.ManyToManyRelation["Dataset"]

    apis = fields.ManyToManyField(
        "models.AgentFunctionCallApi",
        through="agentfunctioncallapi_robot",
        related_name="robots_apis",
        forward_key="agentfunctioncall_id",
        backward_key="robot_id",
    )

    inside_preview_users: fields.ReverseRelation["InsiderPreviewUser"]
    question_recommended: fields.ReverseRelation["QuestionRecommended"]
    robotprompts: fields.ReverseRelation["RobotPrompt"]
    dictionary: fields.ReverseRelation["Dictionary"]
    robot_access_statistics: fields.ReverseRelation["RobotAccessStatistics"]
    robot2agentapisrel: fields.ReverseRelation["AgentFunctionCallApiRobot"]

    @property
    def display_subject_name(self):
        if not self.subject_name:
            return self.name
        else:
            return self.subject_name

    @display_subject_name.setter
    def display_subject_name(self, value):
        self.subject_name = value

    def ai_prompt(self) -> str:
        if self.robotprompts and self.robotprompts[0].prompt:
            return self.robotprompts[0].prompt
        return digital_human_default_prompt

    def context(self) -> str:
        if not self.robotconfigs:
            return ""
        for config in self.robotconfigs:
            if config.key == AIConfigType.ENV_CONTEXT:
                return config.value
        return ""

    def dictionaries(self, query: str) -> list:
        if not self.dictionary:
            return []
        from mygpt.utils import filter_dictionary

        dicts = filter_dictionary(query, self.dictionary)
        return dicts

    def file_count(self) -> int:
        return len(self.file_count)

    def faq_count(self) -> int:
        return self.faq_count

    def all_file_count(self) -> int:
        return len(self.all_file_count)

    def all_faq_count(self) -> int:
        return len(self.all_faq_count)

    def configs(self) -> dict:
        def trans_value(key, value):
            # 指定key值转换为对象
            if key == AIConfigType.FRENQUENT_QUESTION:
                rs = json.loads(value)
                return rs
            elif key == AIConfigType.WEB_SEARCH_SITE_LIST:
                rs = json.loads(value)
                return rs
            elif key == AIConfigType.TRANSFER_TO_HUMAN:
                rs = json.loads(value)
                return rs
            elif key == AIConfigType.ENABLE_ACCESS_CONTROL:
                rs = str(value).lower() == "true"
                return rs
            elif key == AIConfigType.ACCESS_CONTROL_ALLOW_EMAIL_LIST:
                rs = json.loads(value)
                return rs
            elif key == AIConfigType.CHAT_TEMPREATURE:
                rs = float(value)
                return rs
            elif key == AIConfigType.ENABLE_TTS:
                rs = str(value).lower() == "true"
                return rs
            elif key == AIConfigType.ENABLE_2D_DIGITAL_PERSON:
                rs = str(value).lower() == "true"
                return rs
            return value

        rs = {}
        if not self.robotconfigs:
            return rs
        rs[AIConfigType.UNKNOWN_TEXT] = get_unknown_text()
        for config in self.robotconfigs:
            rs[config.key] = trans_value(config.key, config.value)
        # 如果没有数字人prompt，则返回默认prompt
        if AIConfigType.DIGITAL_HUMAN_PROMPT not in rs:
            rs[AIConfigType.DIGITAL_HUMAN_PROMPT] = digital_human_robot_default_prompt
        return rs

    def get_config(self, key: AIConfigType) -> str:
        if not self.robotconfigs:
            return ""
        for config in self.robotconfigs:
            if config.key == key:
                return config.value
        return ""

    def get_embedding_params(self) -> EmbeddingParams:
        embeddings_model_type = self.get_config(AIConfigType.EMBEDDINGS_MODEL)
        provider = EMBEDDINGS_MODEL.OPENAI
        if embeddings_model_type:
            provider = EMBEDDINGS_MODEL(embeddings_model_type)
        embedding_model_name = self.get_config(AIConfigType.EMBEDDING_MODEL_NAME)
        model_name = OpenAIModel.TEXT_EMBEDDING_ADA_002
        if embedding_model_name:
            model_name = OpenAIModel(embedding_model_name)
        embedding_dimensions = self.get_config(AIConfigType.EMBEDDING_DIMENSIONS)
        dimensions = None
        if embedding_dimensions:
            dimensions = int(embedding_dimensions)
        embedding_params = EmbeddingParams(
            provider=provider,
            model_name=model_name,
            dimensions=dimensions,
        )
        return embedding_params

    @classmethod
    async def get_user_robots_count(cls, user_id):
        return await cls.filter(user_id=user_id, deleted_at__isnull=True).count()

    @classmethod
    async def check_deleted(cls, robot_id):
        return not await cls.filter(id=robot_id, deleted_at__isnull=True).exists()

    @classmethod
    async def update_ai_status(cls, ai_id, ai_status: AIStatus):
        await cls.filter(id=ai_id).update(ai_status=ai_status)

    @classmethod
    async def update_ai_status_from_dataset(cls, dataset_id, ai_status: AIStatus):
        """Update ai_status of Robots with a specific dataset_id"""
        robots = await Robot.filter(datasets__id=dataset_id).all()
        for robot in robots:
            await cls.update_ai_status(robot.id, ai_status)

    @classmethod
    async def get_effective_dataset_ids_by_bot_id(cls, robot_id: uuid.UUID):
        datasets = await Dataset.filter(
            robots__id=robot_id,
            deleted_at__isnull=True,
        )
        dataset_ids = [d.id for d in datasets]
        return dataset_ids

    class PydanticMeta:
        exclude = (
            "updated_at",
            "deleted_at",
            "vectors",
            "vectorfiles",
            "vectorfiles.vectors",
            "questionrecords",
            "robot_faqs",
            "robotmd5s",
            "sessions",
            "urls",
            "image_attachments",
            "faq_property",
            "function_call_api",
            "inside_preview_users",
            "user.userconfigs",
            "question_recommended",
            "robotprompts",
            "dictionary",
            "robot_access_statistics",
        )


class RobotConfig(AbstractBaseModel):
    robot: fields.ForeignKeyRelation["Robot"] = fields.ForeignKeyField(
        "models.Robot",
        index=True,
    )
    key = fields.CharEnumField(AIConfigType, max_length=100)
    value = fields.TextField(null=True)

    @classmethod
    async def set_config(cls, ai_id, key: AIConfigType, value):
        return await cls.update_or_create(
            robot_id=ai_id,
            key=key,
            defaults={"value": value},
        )

    @classmethod
    async def set_configs(cls, ai_id, configs: dict):
        for key, value in configs.items():
            await cls.set_config(ai_id, key, value)

    @classmethod
    async def get_config(cls, ai_id, key: AIConfigType):
        config_obj = await cls.get_or_none(robot_id=ai_id, key=key)
        return config_obj.value if config_obj else None

    @classmethod
    async def get_configs(cls, ai_id, keys: List[AIConfigType]):
        configs = await cls.filter(robot_id=ai_id, key__in=keys)
        return {config.key: config.value for config in configs}

    class PydanticMeta:
        exclude = (
            "created_at",
            "updated_at",
            "robot",
            "robot_id",
        )


class RobotPrompt(AbstractBaseModelWithDeletedAt):
    robot: fields.ForeignKeyRelation["Robot"] = fields.ForeignKeyField(
        "models.Robot",
        index=True,
    )
    prompt = fields.TextField(null=True)

    class PydanticMeta:
        exclude = ("robot",)


class Question(AbstractBaseModelWithDeletedAt):
    question = fields.TextField()
    question_type = fields.CharEnumField(
        QuestionType, max_length=40, default=QuestionType.Generator
    )

    robot: fields.ForeignKeyRelation[Robot] = fields.ForeignKeyField(
        "models.Robot",
        related_name="questions",
    )

    class Meta:
        ordering = ["created_at"]

    class PydanticMeta:
        exclude = (
            "created_at",
            "updated_at",
            "deleted_at",
            "robot",
            "robot_id",
            # 'user_id'
        )


class Vector(AbstractBaseModelWithDeletedAt):
    robot: fields.ForeignKeyRelation["Robot"] = fields.ForeignKeyField(
        "models.Robot",
        index=True,
    )
    vector_text = fields.TextField(null=True)
    text_origin = fields.TextField(null=True)
    index_id = fields.CharField(
        max_length=100,
        null=True,
        index=True,
    )
    text_type = fields.CharEnumField(
        VectorTextType, max_length=20, default=VectorTextType.EN
    )
    tokens = fields.IntField(null=True)
    fingerprint = fields.CharField(max_length=100, null=True)
    vector_file: fields.ForeignKeyRelation["VectorFile"] = fields.ForeignKeyField(
        "models.VectorFile",
        null=True,
        index=True,
    )
    status = fields.CharEnumField(
        VectorStatus, max_length=50, default=VectorStatus.INIT
    )
    metadata = fields.JSONField(null=True)

    def text(self) -> str:
        return AESCipher(AES_SECRETS).decrypt(self.text_origin).decode("utf-8")

    class Meta:
        ordering = ["created_at"]

    @classmethod
    async def get_by_index_ids(cls, ai_id: str, index_ids: list[str]):
        if len(index_ids) == 0:
            return []
        vectors = await cls.filter(index_id__in=index_ids, robot_id=ai_id)
        # sort by index_ids
        return sorted(vectors, key=lambda x: index_ids.index(x.index_id))

    class PydanticMeta:
        exclude = (
            "created_at",
            "updated_at",
            "deleted_at",
            "robot",
            "index_id",
        )


class VectorFile(AbstractBaseModelWithDeletedAt):
    key = fields.CharField(max_length=100, null=True, index=True)
    title = fields.CharField(max_length=2048, null=True)
    filename = fields.CharField(
        max_length=2048,
        null=True,
        description="对于网页，存储URL；对于本地上传，存储文件名；对于Lark文档，存储file的name字段",
    )
    file_type = fields.CharEnumField(VectorFileType, max_length=20)
    file_status = fields.CharEnumField(
        VectorFileStatus, max_length=20, default=VectorFileStatus.READY
    )
    file_lang = fields.CharField(max_length=50, null=True)
    file_size = fields.IntField(default=0)
    token_count = fields.IntField(default=0)
    characters_count = fields.IntField(default=0)
    failed_reason = fields.TextField(null=True)
    index_ids = fields.JSONField(null=True)
    metadata = fields.JSONField(null=True)
    resources = fields.JSONField(null=True)
    content_hash = fields.CharField(max_length=255, null=True)

    source_type = fields.CharEnumField(
        VectorFileSourceType,
        max_length=100,
        null=True,
        description="文件来源类型",
    )

    vectors: fields.ReverseRelation["Vector"]
    robot: fields.ForeignKeyRelation["Robot"] = fields.ForeignKeyField(
        "models.Robot",
        index=True,
        null=True,
    )
    dataset: fields.ForeignKeyRelation["Dataset"] = fields.ForeignKeyField(
        "models.Dataset",
        index=True,
        null=True,
    )
    learn_type = fields.IntField(default=VECTOR_FILE_DEFAULT_LEARN_TYPE)

    faq_resources: fields.ReverseRelation["FaqResource"]
    integration_sync_logs: fields.ReverseRelation["IntegrationSyncLog"]
    lark_file: fields.ReverseRelation["LarkFile"]
    integration_rules: fields.ManyToManyRelation["IntegrationRule"]

    class Meta:
        ordering = ["created_at"]
        # indexes = [
        #     ("integration_rule_id", "created_at"),  # 添加复合索引
        # ]
        indexes = [
            # 只添加一个最关键的复合索引，覆盖最耗时查询的场景
            ("dataset_id", "created_at", "file_status", "token_count"),
        ]

    def tokens(self) -> int:
        if self.metadata and "tokens" in self.metadata:
            return self.metadata["tokens"]
        return self.token_count

    @classmethod
    async def get_known_urls(cls, robot_id: str):
        urls = await cls.filter(
            robot_id=robot_id, file_status__not=VectorFileStatus.FAIL
        ).values_list("filename", flat=True)
        known_urls = set()
        for url in urls:
            known_urls.add(url)
        return known_urls

    @classmethod
    async def get_robot_tokens(cls, robot_id: str):
        files = await cls.filter(
            robot_id=robot_id, file_status=VectorFileStatus.COMPLETE
        )
        total_tokens = 0
        for file_obj in files:
            total_tokens += file_obj.tokens()

        return total_tokens

    @classmethod
    async def get_robot_tokens_with_time_period(cls, robot_id: str, date: datetime):
        files = await cls.filter(
            robot_id=robot_id,
            file_status=VectorFileStatus.COMPLETE,
            updated_at__gte=date,
        )
        total_tokens = 0
        for file_obj in files:
            total_tokens += file_obj.tokens()
        return total_tokens

    @classmethod
    async def reset_status(cls, file_id: str, data: dict = None):
        vector_file = await cls.get_or_none(id=file_id)
        if not vector_file:
            return
        new_metadata = vector_file.metadata or {}
        # 如果传入 metadata，那么需要重置原来 vector_file 的 metadata
        if data and "metadata" in data:
            new_metadata = data["metadata"]
            del data["metadata"]
        new_metadata.update(
            {
                "chunk_count": 0,
                "page_size": 0,
            }
        )
        await vector_file.update_from_dict(
            {
                "file_status": VectorFileStatus.READY.value,
                "failed_reason": None,
                "index_ids": [],
                "resources": [],
                "token_count": 0,
                "characters_count": 0,
                "metadata": new_metadata,
                **(data or {}),
            }
        ).save()

    async def delete_search_index(self):
        from mygpt.search_utils import delete_search_indexes

        await delete_search_indexes(self)

    class PydanticMeta:
        exclude = ("deleted_at", "integration_sync_logs")


from tortoise.transactions import in_transaction, F

from tortoise import fields, models


class LarkFile(AbstractBaseModelWithDeletedAt):
    vector_file = fields.OneToOneField(
        "models.VectorFile",
        related_name="lark_file",
        on_delete=fields.CASCADE,
        index=True,
    )

    name = fields.CharField(max_length=1024, null=False)
    type = fields.CharField(max_length=100, null=False)
    token = fields.CharField(max_length=255, null=False, index=True)
    parent_token = fields.CharField(max_length=255, null=True, index=True)
    created_time = fields.CharField(max_length=255, null=False)
    modified_time = fields.CharField(max_length=255, null=False)
    owner_id = fields.CharField(max_length=255, null=True)
    url = fields.CharField(max_length=2048, null=False)

    class Meta:
        table = "lark_file"


class RobotMD5(AbstractBaseModel):
    md5_infos = fields.JSONField()

    robot: fields.ForeignKeyRelation["Robot"] = fields.ForeignKeyField(
        "models.Robot",
        null=True,
    )
    dataset: fields.ForeignKeyRelation["Dataset"] = fields.ForeignKeyField(
        "models.Dataset",
        index=True,
        null=True,
    )

    @classmethod
    async def get_by_robot_id(cls, robot_id: str):
        md5_info_list = await cls.filter(robot_id=robot_id)
        md5_infos = None
        for md5_info in md5_info_list:
            if md5_infos is None:
                md5_infos = md5_info
            else:
                md5_infos.md5_infos.extend(md5_info.md5_infos)
        return md5_infos

    @classmethod
    async def get_by_dataset_id(cls, dataset_id: str):
        md5_info_list = await cls.filter(dataset_id=dataset_id)
        md5_infos = None
        for md5_info in md5_info_list:
            if md5_infos is None:
                md5_infos = md5_info
            else:
                md5_infos.md5_infos.extend(md5_info.md5_infos)
        return md5_infos

    @classmethod
    async def get_md5(cls, robot_id: str, md5: str) -> Md5Info:
        md5_obj = await cls.get_by_robot_id(robot_id)
        if md5_obj:
            for md5_info in md5_obj.md5_infos:
                info = Md5Info.parse_obj(md5_info)
                if md5 == info.md5:
                    return info
        return None

    @classmethod
    async def delete_md5_infos(cls, robot_id: str, md5_infos: List[str]):
        robot_md5_obj = await cls.get_by_robot_id(robot_id)
        if robot_md5_obj:
            # 删除md5_infos中的md5s
            for md5_info in md5_infos:
                for index, info in enumerate(robot_md5_obj.md5_infos):
                    if md5_info.md5 == info["md5"]:
                        if not md5_info.vector_file_ids:
                            robot_md5_obj.md5_infos.pop(index)
                        break
            md5_infos_dict = jsonable_encoder(robot_md5_obj.md5_infos)
            robot_md5_obj.md5_infos = md5_infos_dict
            await robot_md5_obj.save()

    @classmethod
    async def delete_md5_infos_dataset(cls, dataset_id, md5_infos: List[str]):
        robot_md5_obj = await cls.get_by_dataset_id(dataset_id)
        if robot_md5_obj:
            # 删除md5_infos中的md5s
            for md5_info in md5_infos:
                for index, info in enumerate(robot_md5_obj.md5_infos):
                    if md5_info.md5 == info["md5"]:
                        if not md5_info.vector_file_ids:
                            robot_md5_obj.md5_infos.pop(index)
                        break
            md5_infos_dict = jsonable_encoder(robot_md5_obj.md5_infos)
            robot_md5_obj.md5_infos = md5_infos_dict
            await robot_md5_obj.save()

    @classmethod
    async def create_or_update_md5_infos(cls, robot_id: str, md5_infos: List[Md5Info]):
        robot_md5_obj = await cls.get_by_robot_id(robot_id)
        if robot_md5_obj:
            # 合并新的md5_infos到robot_md5_obj.md5_infos列表中
            # 注意，新的md5_infos中可能再md5_infos中已经存在的md5，需要覆盖旧的
            for md5_info in md5_infos:
                md5_info_dict = jsonable_encoder(md5_info)
                for index, info in enumerate(robot_md5_obj.md5_infos):
                    if md5_info.md5 == info["md5"]:
                        robot_md5_obj.md5_infos[index] = md5_info_dict
                        break
                else:
                    robot_md5_obj.md5_infos.append(md5_info_dict)
            md5_infos_dict = jsonable_encoder(robot_md5_obj.md5_infos)
            robot_md5_obj.md5_infos = md5_infos_dict
            await robot_md5_obj.save()
        else:
            md5_infos_dict = jsonable_encoder(md5_infos)
            await cls.create(robot_id=robot_id, md5_infos=md5_infos_dict)

    @classmethod
    async def create_or_update_md5_infos_dataset(
        cls, dataset_id: str, md5_infos: List[Md5Info]
    ):
        robot_md5_obj = await cls.get_by_dataset_id(dataset_id)
        if robot_md5_obj:
            # 合并新的md5_infos到robot_md5_obj.md5_infos列表中
            # 注意，新的md5_infos中可能再md5_infos中已经存在的md5，需要覆盖旧的
            for md5_info in md5_infos:
                md5_info_dict = jsonable_encoder(md5_info)
                for index, info in enumerate(robot_md5_obj.md5_infos):
                    if md5_info.md5 == info["md5"]:
                        robot_md5_obj.md5_infos[index] = md5_info_dict
                        break
                else:
                    robot_md5_obj.md5_infos.append(md5_info_dict)
            md5_infos_dict = jsonable_encoder(robot_md5_obj.md5_infos)
            robot_md5_obj.md5_infos = md5_infos_dict
            await robot_md5_obj.save()
        else:
            md5_infos_dict = jsonable_encoder(md5_infos)
            await cls.create(dataset_id=dataset_id, md5_infos=md5_infos_dict)


class Url(AbstractBaseModelWithDeletedAt):
    robot: fields.ForeignKeyRelation["Robot"] = fields.ForeignKeyField(
        "models.Robot",
    )
    url = fields.CharField(max_length=255)
    status = fields.CharEnumField(
        VectorFileStatus, max_length=20, default=VectorFileStatus.READY
    )
    failed_reason = fields.TextField(null=True)
    metadata = fields.JSONField(null=True)


class Apikey(AbstractBaseModelWithDeletedAt):
    api_key = fields.CharField(max_length=60, unique=True)
    description = fields.CharField(max_length=200, null=True)
    user_id = fields.CharField(max_length=100)
    is_super_admin = fields.BooleanField(default=False)

    class PydanticMeta:
        exclude = ("updated_at", "deleted_at", "id")


class OpenaiApikey(AbstractBaseModelWithDeletedAt):
    api_key = fields.CharField(max_length=60, unique=True)
    # fetch right balance in future
    balance = fields.FloatField(default=18.0)
    # check status via openai in future
    status = fields.CharEnumField(
        enum_type=OpenaiApikeyStatus, default=OpenaiApikeyStatus.NORMAl, max_length=20
    )
    in_use = fields.BooleanField(default=True)

    user: fields.ForeignKeyRelation[User] = fields.ForeignKeyField(
        "models.User",
        related_name="openai_apikeys",
        to_field="user_id",
    )

    class PydanticMeta:
        exclude = (
            "created_at",
            "updated_at",
            "deleted_at",
            "user.userconfigs",
        )


class QuestionRecord(AbstractBaseModelWithDeletedAt):
    user: fields.ForeignKeyRelation[User] = fields.ForeignKeyField(
        "models.User",
        to_field="user_id",
        null=True,
    )
    robot: fields.ForeignKeyRelation["Robot"] = fields.ForeignKeyField(
        "models.Robot",
        index=True,
    )
    question = fields.TextField()
    answer = fields.TextField(null=True)
    total_tokens = fields.IntField()

    @classmethod
    async def create_record(cls, user_id: Optional[str], robot: Robot, question: str):
        record = await cls.create(
            user_id=user_id,
            robot=robot,
            question=question,
            total_tokens=0,
        )
        return record

    class PydanticMeta:
        exclude = (
            "updated_at",
            "deleted_at",
            "id",
            "user.userconfigs",
        )


class Session(AbstractBaseModelWithDeletedAt):
    session_id = fields.CharField(max_length=100, unique=True)
    # title需要在前端完整展示，并且会在没有值的时候将question作为title做为演示，在ai生成title后会保存到session的title中。
    # 后面不会保存到数据库，所以这里需要解除显示限制。
    # 为了确保安全，在下面的save方法中使用截断，确保和数据库的100长度匹配，不会保存错误。
    # 备注：session中的title是100，sessionmessage中是title是1024 朱虹宇 20250412
    title = fields.TextField()
    robot: fields.ForeignKeyRelation["Robot"] = fields.ForeignKeyField(
        "models.Robot",
        index=True,
    )
    is_test = fields.BooleanField(default=False)

    created_by = fields.CharField(max_length=100, null=True)
    created_ip = fields.CharField(max_length=100, null=True)
    user_del = fields.BooleanField(null=True)
    source = fields.CharEnumField(
        MessagePageInfo,
        max_length=100,
        null=True,
        default=MessagePageInfo.PLAYGROUND.value,
    )
    transfer_to_human = fields.BooleanField(default=False)
    session_evaluate: fields.OneToOneRelation["SessionEvaluate"]
    sessionusers: fields.ReverseRelation["SessionUser"]
    sessionmessages: fields.ReverseRelation["SessionMessage"]

    async def save(self, *args, **kwargs):
        # 在保存前检查并截断 title 字段
        if self.title and len(self.title) > 100:
            self.title = self.title[:100]
        await super().save(*args, **kwargs)

    async def add_message(
        cls,
        user_id: str,
        anonymous_username: str,
        question: str,
        message_id: uuid,
        contexts: List[str] = [],
        ref_list: Optional[List[dict]] = None,
        ref_indexes: Optional[List[int]] = None,
        is_test: Optional[bool] = False,
        comes_from: Optional[str] = MESSAGE_COMES_FROM.GREETINGS,
        title: Optional[str] = None,
        url: Optional[str] = None,
    ):
        message = await SessionMessage.create(
            id=message_id,
            session_id=cls.session_id,
            user_id=user_id,
            anonymous_username=anonymous_username,
            question=question,
            contexts=contexts,
            total_tokens=0,
            reference_list=ref_list,
            reference_indexes=ref_indexes,
            comes_from=comes_from,
            is_test=is_test,
            title=title,
            url=url,
            status=SessionMessageStatus.PENDING,
        )
        await cls.update_from_dict(
            {
                "updated_at": datetime.now(),
            }
        ).save()
        return message

    @classmethod
    async def create(
        cls, using_db: Optional[models.BaseDBAsyncClient] = None, **kwargs: Any
    ):
        session = await super().create(using_db=using_db, **kwargs)
        await SessionEvaluate.create(session=session)
        return session

    class PydanticMeta:
        exclude = (
            "deleted_at",
            "id",
            "sessionusers",
            "robot",
        )


class SessionUser(AbstractBaseModelWithDeletedAt):
    session: fields.ForeignKeyRelation["Session"] = fields.ForeignKeyField(
        "models.Session",
        to_field="session_id",
        index=True,
    )
    # 如果是gbase用户，写到这里
    user: fields.ForeignKeyRelation[User] = fields.ForeignKeyField(
        "models.User",
        to_field="user_id",
        null=True,
    )

    # 如果是匿名用户，写到这里
    anonymous_username = fields.CharField(max_length=100, null=True)

    @classmethod
    async def add_if_not_exist(
        cls,
        session_id: str,
        user_id: str,
        anonymous_username: str,
    ):
        try:
            session_user = await cls.get_or_create(
                session_id=session_id,
                user_id=user_id,
                anonymous_username=anonymous_username,
            )
        except MultipleObjectsReturned:
            session_user = await cls.filter(
                session_id=session_id,
                user_id=user_id,
                anonymous_username=anonymous_username,
            ).first()
        return session_user

    class PydanticMeta:
        exclude = (
            "updated_at",
            "deleted_at",
            "id",
            "user",
            "session",
        )


class SessionMessage(AbstractBaseModelWithDeletedAt):
    session: fields.ForeignKeyRelation["Session"] = fields.ForeignKeyField(
        "models.Session",
        to_field="session_id",
        index=True,
    )
    user: fields.ForeignKeyRelation[User] = fields.ForeignKeyField(
        "models.User",
        to_field="user_id",
        null=True,
    )
    anonymous_username = fields.CharField(max_length=100, null=True)

    question = fields.TextField()
    answer = fields.TextField(null=True)
    feedback_content = fields.TextField(null=True)
    contexts = fields.JSONField(null=True, default=[])
    total_tokens = fields.IntField()
    rating = fields.IntField(default=0, null=True)
    reference_list = fields.JSONField(null=True)
    reference_indexes = fields.JSONField(null=True)
    history_conversation_count = fields.IntField(null=True)
    is_test = fields.BooleanField(default=False)
    faq_id = fields.UUIDField(null=True, index=True)
    comes_from = fields.CharField(max_length=100, null=True)
    first_response_time = fields.IntField(null=True)
    llm_can_answer = fields.BooleanField(null=True)
    title = fields.CharField(max_length=1024, null=True)
    url = fields.CharField(max_length=1024, null=True)
    model = fields.CharEnumField(OpenAIModel, max_length=100, null=True)
    question_metadata = fields.JSONField(null=True)
    status = fields.CharEnumField(
        SessionMessageStatus, max_length=100, default=SessionMessageStatus.FINISHED
    )
    is_correct = fields.BooleanField(null=True, default=False)
    session_message_metadata: fields.ReverseRelation["SessionMessageMetadata"]
    transfer_to_human = fields.BooleanField(default=False)
    # 当前消息的 GBase Canvas 信息（目前有url, title）（可能有、可能没有）
    canvas = fields.JSONField(null=True)

    class Meta:
        indexes = (
            # 根据查询条件创建复合索引
            ("session_id", "is_test", "total_tokens", "updated_at"),
        )

    def is_unanswerable(self) -> bool:
        return self.llm_can_answer is False

    @classmethod
    async def set_unanswerable(cls, id: uuid.UUID, unanswerable: bool):
        message = await cls.get_or_none(id=id)
        if not message:
            return
        message.llm_can_answer = None
        is_unanswerable = message.is_unanswerable()
        message.llm_can_answer = unanswerable == False
        await message.save(update_fields=["llm_can_answer"])
        if not is_unanswerable and unanswerable:
            await SessionEvaluate.add_unanswerable_count(session_id=message.session_id)

    @classmethod
    async def bind_faq(cls, message_id: uuid.UUID, faq_id: uuid.UUID):
        message = await cls.get_or_none(id=message_id)
        if not message:
            return
        if not message.is_correct:
            await SessionEvaluate.add_correct_count(session_id=message.session_id)

            message.is_correct = True
        message.faq_id = faq_id
        await message.save()
        return message

    @classmethod
    async def unbind_faq(cls, message_id: uuid.UUID):
        message = await cls.get_or_none(id=message_id)
        if not message:
            return
        if message.is_correct:
            await SessionEvaluate.add_correct_count(
                session_id=message.session_id, value=-1
            )
        message.faq_id = None
        message.is_correct = False
        await message.save()
        return message

    @classmethod
    async def remove_faqs(cls, faq_ids: List[uuid.UUID | str]):
        messages = await cls.filter(faq_id__in=faq_ids)
        for message in messages:
            is_correct = message.is_correct
            if is_correct:
                await SessionEvaluate.add_correct_count(
                    session_id=message.session_id, value=-1
                )
            message.faq_id = None
            message.is_correct = False
            await message.save()

    class PydanticMeta:
        exclude = (
            "updated_at",
            "deleted_at",
            "session",
            "user",
        )


class SessionMessageMetadata(AbstractBaseModelWithDeletedAt):
    session_message: fields.ForeignKeyRelation["SessionMessage"] = (
        fields.ForeignKeyField(
            "models.SessionMessage",
            index=True,
            null=True,
            related_name="session_message_metadata",
        )
    )

    key = fields.CharField(max_length=100)

    value = fields.CharField(max_length=100)

    class PydanticMeta:
        exclude = "session_message"


class SessionEvaluate(AbstractBaseModelWithDeletedAt):
    """Session 评分"""

    session: fields.OneToOneRelation["Session"] = fields.OneToOneField(
        "models.Session",
        related_name="session_evaluate",
        to_field="session_id",
        on_delete=fields.CASCADE,
        index=True,
    )

    unanswerable_count = fields.IntField(default=0)

    correct_count = fields.IntField(default=0)

    message_count = fields.IntField(default=0)

    score = fields.FloatField(default=0, null=True)

    @classmethod
    async def add_unanswerable_count(cls, session_id: str, value: int = 1):
        evaluate = await cls.get_or_none(session_id=session_id)
        if not evaluate:
            return
        evaluate.unanswerable_count += value
        evaluate.unanswerable_count = max(0, evaluate.unanswerable_count)
        await evaluate.save()

    @classmethod
    async def add_correct_count(cls, session_id: str, value: int = 1):
        evaluate = await cls.get_or_none(session_id=session_id)
        if not evaluate:
            return
        evaluate.correct_count += value
        evaluate.correct_count = max(0, evaluate.correct_count)
        await evaluate.save()

    @classmethod
    async def add_message_count(cls, session_id: str, value: int = 1):
        # 先尝试更新已存在的记录
        updated = await cls.filter(session_id=session_id).update(
            message_count=F("message_count") + value
        )

        if not updated:
            # 如果没有更新任何记录，说明记录不存在，创建新记录
            evaluate = await cls.create(session_id=session_id, message_count=value)
        else:
            # 获取更新后的记录
            evaluate = await cls.get(session_id=session_id)

        # 确保不小于0
        if evaluate.message_count < 0:
            await cls.filter(id=evaluate.id).update(message_count=0)
            evaluate = await cls.get(id=evaluate.id)

        return evaluate

    class PydanticMeta:
        exclude = (
            "updated_at",
            "deleted_at",
            "session",
        )


class ImageAttachment(AbstractBaseModel):
    key = fields.CharField(max_length=255, unique=True)
    filename = fields.CharField(max_length=255, null=True)
    file_type = fields.CharEnumField(FileType, max_length=20, default=FileType.ORIGINAL)
    data_id = fields.CharField(max_length=255, null=True)
    data_type = fields.CharEnumField(
        FileSource, max_length=20, default=FileSource.ROBOT
    )
    file_storage = fields.CharEnumField(
        FileStorage, max_length=20, default=FileStorage.S3
    )
    robot: fields.ForeignKeyRelation["Robot"] = fields.ForeignKeyField(
        "models.Robot",
        null=True,
        related_name="image_attachments",
    )

    class PydanticMeta:
        exclude = ("created_at", "updated_at")
        computed = ("url",)

    def url(self) -> str:
        if self.file_storage == FileStorage.S3:
            from mygpt.utils import get_file_url_from_s3

            return get_file_url_from_s3(self.key, AWS_S3_BUCKET_NAME)
        else:
            from mygpt.utils import get_file_url_from_local

            return get_file_url_from_local(self.key)


class StripeWebhookLog(AbstractBaseModelWithDeletedAt):
    type = fields.CharField(max_length=255, null=True)
    data = fields.JSONField(null=True)
    run_sta = fields.CharField(max_length=255, null=True)


class StripeProducts(AbstractBaseModelWithDeletedAt):
    name = fields.CharField(max_length=255, null=True)
    max_questions = fields.IntField(null=True, default=0)
    max_tokens = fields.IntField(null=True, default=0)
    max_upload_file = fields.IntField(null=True, default=0)
    param_ext = fields.JSONField(null=True)
    api_id = fields.CharField(max_length=255, null=True)
    order = fields.IntField(null=True, default=0)
    month = fields.IntField(null=True, default=0)
    mode = fields.CharEnumField(
        StripeModel, null=True, default=StripeModel.SUBSCRIPTION
    )
    product = fields.CharField(max_length=255, null=True)

    class PydanticMeta:
        exclude = (
            "updated_at",
            "deleted_at",
            "stripe_products_stripe_payments",
        )


class StripePayments(AbstractBaseModelWithDeletedAt):
    api_id = fields.CharField(max_length=255, null=True)
    sub_id = fields.CharField(max_length=255, null=True)
    payment_id = fields.CharField(max_length=255, null=True)
    type = fields.CharField(max_length=255, null=True)
    invoice = fields.CharField(max_length=255, null=True)
    customer_details = fields.JSONField(null=True)
    canceled_at = fields.DatetimeField(null=True)
    end_at = fields.DatetimeField(null=True)
    mode = fields.CharEnumField(
        StripeModel, null=True, default=StripeModel.SUBSCRIPTION
    )

    stripe_products = fields.ForeignKeyField(
        "models.StripeProducts",
        related_name="stripe_products_stripe_payments",
        null=True,
    )

    user = fields.ForeignKeyField(
        "models.User", related_name="user_stripe_payments", null=True
    )

    exclude = ("deleted_at", "user")


class FaqsConfig(AbstractBaseModelWithDeletedAt):
    min_score = fields.FloatField(null=True)

    class PydanticMeta:
        exclude = (
            "created_at",
            "updated_at",
            "deleted_at",
        )


class Faqs(AbstractBaseModelWithDeletedAt):
    sources = fields.CharField(max_length=255, null=True)
    source_type = fields.CharEnumField(
        FaqSourceType, max_length=100, default=FaqSourceType.USER_INPUT, null=True
    )
    excel_id = fields.CharField(max_length=255, null=True)
    question = fields.TextField(null=True)
    answer = fields.TextField(null=True)
    property_info = fields.JSONField(null=True)
    rating = fields.IntField(default=1, null=True)
    citation = fields.JSONField(null=True)
    has_image = fields.BooleanField(default=False)
    answer_token_count = fields.IntField(default=0, null=True)
    question_token_count = fields.IntField(default=0, null=True)
    answer_characters_count = fields.IntField(default=0, null=True)
    question_characters_count = fields.IntField(default=0, null=True)
    session_message_id = fields.UUIDField(null=True)
    similar_questions = fields.JSONField(null=True)
    recommended_questions = fields.JSONField(null=True)
    hierarchy_parent_id = fields.CharField(max_length=255, null=True)
    hierarchy_level = fields.IntField(default=0, null=True)
    hierarchy_path = fields.CharField(max_length=255, null=True)
    is_search = fields.BooleanField(default=True)
    similar_questions_ids = fields.JSONField(null=True)
    type = fields.CharEnumField(
        FAQ_TYPE, max_length=100, null=True, default=FAQ_TYPE.ANSWER
    )

    robot: fields.ForeignKeyRelation["Robot"] = fields.ForeignKeyField(
        "models.Robot",
        index=True,
        null=True,
        related_name="robot_faqs",
    )
    user: fields.ForeignKeyRelation[User] = fields.ForeignKeyField(
        "models.User",
        to_field="user_id",
        null=True,
        related_name="user_faqs",
    )

    config: fields.ForeignKeyRelation[FaqsConfig] = fields.ForeignKeyField(
        "models.FaqsConfig",
        null=True,
        related_name="faqs_config",
    )

    dataset: fields.ForeignKeyRelation["Dataset"] = fields.ForeignKeyField(
        "models.Dataset",
        index=True,
        null=True,
    )

    resources: fields.ReverseRelation["FaqResource"]

    @classmethod
    async def create_record(
        cls,
        user_id: uuid,
        question: str,
        answer: str,
        excel_id: Optional[str] = None,
        sources: Optional[str] = None,
        property_info: Optional[list] = None,
        rating: Optional[int] = 1,
        **kwargs: Any,
    ):
        record = await cls.create(
            user_id=user_id,
            question=question,
            answer=answer,
            excel_id=excel_id,
            sources=sources[:255] if sources else None,
            property_info=property_info,
            rating=rating,
            **kwargs,
        )
        return record

    @classmethod
    async def add_resource(
        cls,
        faq_id: uuid,
        vector_file_id: uuid,
        page_numbers: Optional[List[int]] = None,
    ):
        resource = await FaqResource.create(
            faq_id=faq_id,
            vector_file_id=vector_file_id,
            page_numbers=page_numbers,
        )
        return resource

    @classmethod
    async def delete_resource(cls, faq_id: uuid):
        await FaqResource.filter(faq_id=faq_id).delete()

    @classmethod
    async def delete_resource_by_vector_id(cls, vector_file_id: uuid):
        await FaqResource.filter(vector_file_id=vector_file_id).delete()

    async def get_related_vector_files(self):
        return await VectorFile.filter(faq_resources__faq_id=self.id).all()

    class PydanticMeta:
        exclude = (
            "deleted_at",
            "dataset",
            "user.userconfigs",
        )


# @pre_save(Faqs)
# async def strip_question(sender, instance: Faqs, using_db, update_fields):
#     if instance.question:
#         if isinstance(instance.question, str):
#             instance.question = instance.question.strip()
#         else:
#             logging.error(f"question is not a string: {instance.question}")


class FaqResource(AbstractBaseModelWithDeletedAt):
    faq: fields.ForeignKeyRelation["Faqs"] = fields.ForeignKeyField(
        "models.Faqs",
        index=True,
        null=True,
        related_name="resources",
    )
    vector_file: fields.ForeignKeyRelation["VectorFile"] = fields.ForeignKeyField(
        "models.VectorFile",
        index=True,
        null=True,
        related_name="faq_resources",
    )

    page_numbers = fields.JSONField(null=True)

    class PydanticMeta:
        exclude = (
            "deleted_at",
            "faq",
        )


class FaqProperty(AbstractBaseModelWithDeletedAt):
    title = fields.CharField(max_length=50, null=True)
    type = fields.CharField(max_length=255, null=True)
    description = fields.CharField(max_length=255, null=True)

    robot: fields.ForeignKeyRelation["Robot"] = fields.ForeignKeyField(
        "models.Robot",
        index=True,
        null=True,
        related_name="faq_property",
    )

    dataset: fields.ForeignKeyRelation["Dataset"] = fields.ForeignKeyField(
        "models.Dataset",
        index=True,
        null=True,
    )

    class PydanticMeta:
        exclude = (
            "updated_at",
            "deleted_at",
            "robot",
            "dataset",
        )


class FunctionCallApi(AbstractBaseModelWithDeletedAt):
    function_call_name = fields.CharField(max_length=255, null=True)
    function_call_description = fields.CharField(max_length=255, null=True)
    openapi_url = fields.CharField(max_length=255, null=True)
    path = fields.CharField(max_length=255, null=True)
    method = fields.CharField(max_length=255, null=True)
    header_authorization = fields.JSONField(null=True)
    server_url = fields.CharField(max_length=255, null=True)
    matching_keywords = fields.JSONField(null=True)
    llm_sta = fields.BooleanField(default=True)
    auth_token_curl = fields.JSONField(null=True)
    body = fields.JSONField(null=True)
    body_required = fields.JSONField(null=True)
    request_explanation = fields.CharField(max_length=255, null=True)
    robot: fields.ForeignKeyRelation["Robot"] = fields.ForeignKeyField(
        "models.Robot",
        index=True,
        null=True,
        related_name="function_call_api",
    )
    dataset: fields.ForeignKeyRelation["Dataset"] = fields.ForeignKeyField(
        "models.Dataset",
        index=True,
        null=True,
    )

    class PydanticMeta:
        exclude = (
            "updated_at",
            "deleted_at",
            "robot",
            "dataset",
        )


class Dataset(AbstractBaseModelWithDeletedAt):
    data_status = fields.CharEnumField(DATASET_STATUS, default=DATASET_STATUS.INIT)
    name = fields.CharField(max_length=255)
    description = fields.TextField(null=True)
    collection_name = fields.CharField(max_length=36, null=True)
    metadata = fields.JSONField(null=True)
    url_rules = fields.JSONField(default=list, null=True)  # 默认为空列表

    user: fields.ForeignKeyRelation[User] = fields.ForeignKeyField(
        "models.User",
        related_name="datasets",
        to_field="user_id",
    )
    robots: fields.ManyToManyRelation["Robot"] = fields.ManyToManyField(
        "models.Robot", related_name="datasets", through="dataset_robot"
    )
    vectorfiles: fields.ReverseRelation["VectorFile"]
    robotmd5s: fields.ReverseRelation["RobotMD5"]
    faqss: fields.ReverseRelation["Faqs"]
    faq_propertys: fields.ReverseRelation["FaqProperty"]
    functioncallapis: fields.ReverseRelation["FunctionCallApi"]
    lark_integration_rules: fields.ReverseRelation["LarkIntegrationRule"]

    @classmethod
    async def create_from_robot_id(cls, robot_obj: Robot):
        # 启用数据库事务
        async with in_transaction("default") as conn:
            # 创建Dataset
            configs = robot_obj.robotconfigs
            metadata = {}
            for config in configs:
                metadata[config.key] = config.value
            dataset_obj = await cls.get_or_none(collection_name=str(robot_obj.id))
            if not dataset_obj:
                # 防止一个bot创建多份默认的数据集
                dataset_obj = await Dataset.create(
                    user_id=robot_obj.user_id,
                    name=f"{robot_obj.name}'s dataset",
                    description=robot_obj.discrible,
                    collection_name=str(robot_obj.id),
                    data_status=DATASET_STATUS.READY,
                    created_at=robot_obj.created_at,
                    metadata=metadata,
                    using_db=conn,
                )
                await dataset_obj.robots.add(robot_obj)
            ai_id = robot_obj.id
            if (
                await RobotMD5.filter(dataset_id=dataset_obj.id).using_db(conn).count()
                > 0
            ):
                return
            # 绑定到Robot
            # 修改vector_file的robot_id到dataset_id
            await VectorFile.filter(robot_id=ai_id).using_db(conn).update(
                dataset_id=dataset_obj.id
            )
            # 修改robot_md5的robot_id到dataset_id
            await RobotMD5.filter(robot_id=ai_id).using_db(conn).update(
                dataset_id=dataset_obj.id
            )
            # 修改faqs的robot_id到dataset_id
            await Faqs.filter(robot_id=ai_id).using_db(conn).update(
                dataset_id=dataset_obj.id
            )
            # 修改faqs property的robot_id到dataset_id
            await FaqProperty.filter(robot_id=ai_id).using_db(conn).update(
                dataset_id=dataset_obj.id
            )
            # 修改function call api的robot_id到dataset_id
            await FunctionCallApi.filter(robot_id=ai_id).using_db(conn).update(
                dataset_id=dataset_obj.id
            )

    @classmethod
    async def get_dataset_ids_by_robot_id(cls, robot_id: str):
        datasets = await Dataset.filter(
            robots__id=robot_id,
            deleted_at__isnull=True,
        )
        dataset_ids = [d.id for d in datasets]
        return dataset_ids

    def get_embedding_params(self) -> EmbeddingParams:
        embeddings_model_type = self.metadata.get(AIConfigType.EMBEDDINGS_MODEL)
        provider = EMBEDDINGS_MODEL.OPENAI
        if embeddings_model_type:
            provider = EMBEDDINGS_MODEL(embeddings_model_type)
        embedding_model_name = self.metadata.get(AIConfigType.EMBEDDING_MODEL_NAME)
        model_name = OpenAIModel.TEXT_EMBEDDING_ADA_002
        if embedding_model_name:
            model_name = OpenAIModel(embedding_model_name)
        embedding_dimensions = self.metadata.get(AIConfigType.EMBEDDING_DIMENSIONS)
        dimensions = None
        if embedding_dimensions:
            dimensions = int(embedding_dimensions)

        return EmbeddingParams(
            provider=provider,
            model_name=model_name,
            dimensions=dimensions,
        )

    class Meta:
        ordering = ["-created_at"]

    class PydanticMeta:
        exclude = (
            "deleted_at",
            "vectorfiles",
            "robotmd5s",
            "user",
            "faqss",
            "faqpropertys",
            "functioncallapis",
            "collection_name",
            "robots.questions",
            "robots.user",
            "robots.robotconfigs",
            "integration_rules",
        )


class IntegrationRule(AbstractBaseModelWithDeletedAt):
    name = fields.CharField(max_length=255, default="", null=False)
    dataset = fields.ForeignKeyField(
        "models.Dataset",
        related_name="integration_rules",
        on_delete=fields.CASCADE,
        index=True,
    )
    type = fields.CharEnumField(IntegrationRuleType, max_length=100)
    file_list_sync_status = fields.CharEnumField(
        IntegrationRuleFileListSyncingStatus,
        max_length=100,
        default=IntegrationRuleFileListSyncingStatus.WAITING,
    )
    vector_files: fields.ManyToManyRelation["VectorFile"] = fields.ManyToManyField(
        "models.VectorFile",
        related_name="integration_rules",
        through="integrationrule_vectorfile",
    )
    created_by: fields.ForeignKeyRelation[User] = fields.ForeignKeyField(
        "models.User",
        related_name="created_integration_rules",
        to_field="id",
        null=True,
    )
    notification_emails = fields.JSONField(null=True)
    sync_interval = fields.IntField(null=True, comment="sync interval in seconds")
    last_synced_at = fields.DatetimeField(null=True)
    lark_integration_rule = fields.ReverseRelation["LarkIntegrationRule"]

    class Meta:
        table = "integration_rule"

    class PydanticMeta:
        exclude = (
            "deleted_at",
            "dataset",
            "vector_files",
            "lark_integration_rule",
            "created_by.userconfigs",
        )


class IntegrationRuleVectorFile(AbstractBaseModelWithDeletedAt):
    integration_rule: fields.ForeignKeyRelation[IntegrationRule] = (
        fields.ForeignKeyField(
            "models.IntegrationRule",
            on_delete=fields.CASCADE,
            index=True,
        )
    )
    vectorfile: fields.ForeignKeyRelation[VectorFile] = fields.ForeignKeyField(
        "models.VectorFile",
        on_delete=fields.CASCADE,
        index=True,
    )

    class Meta:
        table = "integrationrule_vectorfile"
        unique_together = (("integration_rule", "vectorfile"),)

    class PydanticMeta:
        exclude = ("deleted_at",)


class LarkIntegrationRule(AbstractBaseModelWithDeletedAt):
    integration_rule = fields.OneToOneField(
        "models.IntegrationRule", on_delete=fields.CASCADE
    )
    app_id = fields.CharField(max_length=100)
    app_secret = fields.CharField(max_length=100)
    share_urls: fields.ReverseRelation["LarkShareUrl"]

    # sync_interval = fields.IntField(null=True, comment="sync interval in seconds")
    # last_synced_at = fields.DatetimeField(null=True)

    class Meta:
        table = "lark_integration_rule"

    def __init__(self, dataset=None, **kwargs):
        if "integration_rule" not in kwargs:
            if dataset is None:
                raise ValueError(
                    "dataset must be provided if integration_rule is not provided."
                )
            integration_rule = IntegrationRule(
                dataset=dataset, type=IntegrationRuleType.LARK
            )
            kwargs["integration_rule"] = integration_rule
        else:
            kwargs["integration_rule"].type = IntegrationRuleType.LARK
        super().__init__(**kwargs)

    async def save(self, *args, **kwargs):
        # 确保 source_type 正确
        if self.integration_rule.type != IntegrationRuleType.LARK:
            raise ValueError("source_type of LarkIntegrationRule must be 'LARK'")

        await self.integration_rule.save()  # 保存基类实例
        await super().save(*args, **kwargs)

    @classmethod
    async def create_with_integration_rule(
        cls,
        dataset: Dataset,
        rule: LarkIntegrationRuleInput,
        user: User,
        using_db=None,
    ):
        # 创建并保存 IntegrationRule 实例
        integration_rule = await IntegrationRule.create(
            name=rule.name,
            dataset=dataset,
            type=IntegrationRuleType.LARK,
            sync_interval=rule.sync_interval,
            using_db=using_db,
            file_list_sync_status=IntegrationRuleFileListSyncingStatus.WAITING,
            last_synced_at=datetime.now(timezone.utc),
            created_by=user,
            notification_emails=rule.notification_emails,
        )

        # 创建并保存 LarkIntegrationRule 实例
        lark_integration_rule = await cls.create(
            integration_rule=integration_rule,
            app_id=rule.app_id,
            app_secret=rule.app_secret,
            using_db=using_db,
        )

        # 创建并保存 LarkShareUrl 实例
        for share_url in rule.share_urls:
            await LarkShareUrl.create(
                integration_rule=lark_integration_rule,
                url=share_url.url,
                recursive=share_url.recursive,
                using_db=using_db,
            )

        return lark_integration_rule


class LarkShareUrl(AbstractBaseModelWithDeletedAt):
    url = fields.CharField(max_length=2048)
    recursive = fields.BooleanField()
    integration_rule = fields.ForeignKeyField(
        "models.LarkIntegrationRule",
        related_name="share_urls",
        on_delete=fields.CASCADE,
    )

    class Meta:
        table = "lark_share_url"

    class PydanticMeta:
        exclude = ("deleted_at",)


class IntegrationSyncStatus(str, Enum):
    SUCCESS = "success"
    FAILURE = "failure"
    RUNNING = "running"


class IntegrationSyncLog(AbstractBaseModelWithDeletedAt):
    integration_rule: fields.ForeignKeyRelation[IntegrationRule] = (
        fields.ForeignKeyField(
            "models.IntegrationRule",
            related_name="integration_sync_logs",
            to_field="id",
        )
    )
    vectorfile: fields.ForeignKeyRelation[VectorFile] = fields.ForeignKeyField(
        "models.VectorFile",
        related_name="integration_sync_logs",
        to_field="id",
    )
    operation = fields.CharEnumField(IntegrationSyncOperation)
    # 触发人，如果为空，则表示是定时自动触发的
    trigger_user: fields.ForeignKeyRelation[User] = fields.ForeignKeyField(
        "models.User",
        related_name="integration_sync_logs",
        to_field="id",
        null=True,
    )
    # 触发类型
    trigger_type = fields.CharEnumField(IntegrationSyncTriggerType)

    sync_status = fields.CharEnumField(IntegrationSyncStatus, max_length=20, null=True)
    failure_reason = fields.TextField(null=True)

    class Meta:
        table = "integration_sync_log"


class InsiderPreviewUser(AbstractBaseModelWithDeletedAt):
    user: fields.ForeignKeyRelation[User] = fields.ForeignKeyField(
        "models.User",
        related_name="inside_preview_users",
        to_field="user_id",
        null=True,
    )
    robot: fields.ForeignKeyRelation[Robot] = fields.ForeignKeyField(
        "models.Robot",
        related_name="inside_preview_users",
        to_field="id",
        null=True,
    )

    @classmethod
    async def is_inside_preview_user(cls, user_id: str):
        user = await cls.get_or_none(user_id=user_id)
        if user:
            return True
        return False

    class PydanticMeta:
        exclude = (
            "deleted_at",
            "robot.user",
            "user.userconfigs",
            "robot.robotconfigs",
            "robot.datasets",
            "robot.questions",
        )


class Prompt(AbstractBaseModelWithDeletedAt):
    version = fields.CharField(max_length=255, null=True)
    prompt_type = fields.CharEnumField(PROMPT_TYPE, max_length=100)
    is_insider = fields.BooleanField(default=True)
    content = fields.JSONField()

    class Meta:
        ordering = ["-created_at"]


class ExcelTestTask(AbstractBaseModelWithDeletedAt):
    user: fields.ForeignKeyRelation[User] = fields.ForeignKeyField(
        "models.User",
        to_field="user_id",
        null=True,
        related_name="user_excel_test_tasks",
    )
    filename = fields.CharField(max_length=255, null=True)
    status = fields.CharEnumField(ExcelTestTaskStatus, null=True)
    pass_count = fields.IntField(null=True, default=0)
    fail_count = fields.IntField(null=True, default=0)
    total_count = fields.IntField(null=True, default=0)
    processed_count = fields.IntField(null=True, default=0)
    input_file_url = fields.CharField(max_length=1024, null=True)
    output_file_url = fields.CharField(max_length=1024, null=True)
    is_scheduled = fields.BooleanField(default=False)
    average_response_time = fields.FloatField(null=True, default=None)

    @classmethod
    async def create_record(cls, user_id: uuid, robot: Robot, excel_id: str):
        record = await cls.create(
            user_id=user_id,
            robot=robot,
            excel_id=excel_id,
        )
        return record

    class PydanticMeta:
        exclude = ("deleted_at",)


class TimedExcelTestFile(AbstractBaseModelWithDeletedAt):
    name = fields.CharField(max_length=1024)
    unique_name = fields.CharField(max_length=1124)
    s3_file_url = fields.CharField(max_length=2000)
    env = fields.CharField(max_length=255, default="PROD")

    class Meta:
        ordering = ["-created_at"]


class SessionMetricsHourly(AbstractBaseModel):
    robot_id = fields.UUIDField()
    hour_of_day = fields.IntField(null=True)
    date = fields.DateField(null=True)
    message_count = fields.IntField(null=True, default=0)
    like_count = fields.IntField(null=True, default=0)
    dislike_count = fields.IntField(null=True, default=0)
    created_session_count = fields.IntField(null=True, default=0)
    new_users = fields.JSONField(null=True, default=list())
    old_users = fields.JSONField(null=True, default=list())
    evaluation_score_count = fields.IntField(null=True, default=0)
    evaluation_score_value = fields.FloatField(null=True, default=0)
    evaluation_answer_count = fields.IntField(null=True, default=0)
    evaluation_source_count = fields.IntField(null=True, default=0)
    unanswered_count = fields.IntField(null=True, default=0)

    class Meta:
        indexes = ("robot_id",)


class SessionMessagePageAccessInfo(AbstractBaseModel):
    robot_id = fields.UUIDField()
    title = fields.CharField(max_length=1024, null=True)
    url = fields.CharField(max_length=1024, null=True)
    source = fields.CharEnumField(
        MessagePageInfo,
        max_length=100,
        null=True,
        default=MessagePageInfo.PLAYGROUND.value,
    )

    class Meta:
        indexes = ("robot_id",)


class SessionMessagePageAccessMetrics(AbstractBaseModel):
    page_access_info: fields.ForeignKeyRelation[SessionMessagePageAccessInfo] = (
        fields.ForeignKeyField(
            "models.SessionMessagePageAccessInfo",
            related_name="session_message_page_access_metrics",
            to_field="id",
        )
    )
    message_count = fields.IntField(null=True, default=0)


class SignatureStatistics(AbstractBaseModel):
    robot_id = fields.UUIDField(null=True)
    session_id = fields.CharField(max_length=100)
    total_count = fields.IntField(null=True, default=0)
    icon = fields.CharField(max_length=100, null=True)
    content = fields.CharField(max_length=100, null=True)

    class Meta:
        indexes = ("robot_id",)


class QuestionRecommended(AbstractBaseModel):
    question = fields.CharField(max_length=1500, null=True)
    recommended_questions = fields.JSONField(null=True)
    message_id = fields.UUIDField(null=True)

    robot: fields.ForeignKeyRelation["Robot"] = fields.ForeignKeyField(
        "models.Robot",
        index=True,
        related_name="question_recommended",
    )

    @classmethod
    async def create_record(
        cls,
        robot_id: uuid,
        question: str,
        recommended_questions: dict,
        message_id: uuid,
    ):
        record = await cls.create(
            robot_id=robot_id,
            question=question,
            recommended_questions=recommended_questions,
            message_id=message_id,
        )
        return record

    class PydanticMeta:
        exclude = ("robot",)


class AccountMember(AbstractBaseModelWithDeletedAt):
    """
    user_id,resource_id,resouce_type,member,
    """

    resource_id = fields.CharField(max_length=100)
    resource_type = fields.CharEnumField(
        ResourceType, max_length=100, default=ResourceType.ROBOT
    )
    user: fields.ForeignKeyRelation[User] = fields.ForeignKeyField(
        "models.User",
        to_field="user_id",
        related_name="account",
    )
    member: fields.ForeignKeyRelation[User] = fields.ForeignKeyField(
        "models.User",
        to_field="user_id",
        related_name="member",
    )

    class PydanticMeta:
        exclude = (
            "updated_at",
            "deleted_at",
            "user",
            "user_id",
            "member.userconfigs",
        )


class Dictionary(AbstractBaseModel):
    source = fields.CharField(max_length=50, null=True)
    target = fields.CharField(max_length=200, null=True)

    robot: fields.ForeignKeyRelation["Robot"] = fields.ForeignKeyField(
        "models.Robot",
        index=True,
        related_name="dictionary",
    )

    class PydanticMeta:
        exclude = ("robot",)
        ordering = ["-updated_at"]


class RobotAccessStatistics(AbstractBaseModel):
    type = fields.CharEnumField(
        RobotAccessType,
        max_length=100,
        default=RobotAccessType.PV,
    )
    date = fields.DateField(null=True)
    count = fields.IntField(null=True, default=0)
    url = fields.CharField(max_length=1000, null=True)

    robot: fields.ForeignKeyRelation["Robot"] = fields.ForeignKeyField(
        "models.Robot",
        index=True,
        related_name="robot_access_statistics",
    )

    class PydanticMeta:
        exclude = ("robot",)
        ordering = ["-updated_at"]


class AgentFunctionCallApi(AbstractBaseModelWithDeletedAt):
    """
    Agent的FunctionCall工具的定义表
    """

    function_call_name = fields.CharField(max_length=255, null=True)
    openapi_url = fields.CharField(max_length=255, null=True)
    server_url = fields.CharField(max_length=255, null=True)
    path = fields.CharField(max_length=255, null=True)
    method = fields.CharField(max_length=255, null=True)
    header_authorization = fields.JSONField(null=True)
    function_type = fields.CharField(max_length=50, null=True)
    function_status = fields.CharField(max_length=50, null=True)
    share_status = fields.CharField(max_length=50, null=True)
    # user: fields.ForeignKeyRelation["User"] = fields.ForeignKeyField(
    #     "models.User",
    #     related_name="agent_function_call_api",
    #     to_field="user_id",
    # )
    user_id = fields.CharField(max_length=100, null=True)
    function_call_description = fields.CharField(max_length=255, null=True)
    matching_keywords = fields.JSONField(null=True)
    llm_sta = fields.BooleanField(default=True)
    body = fields.JSONField(null=True)
    auth_token_curl = fields.JSONField(null=True)
    body_required = fields.JSONField(null=True)
    request_explanation = fields.CharField(max_length=255, null=True)

    # robots = fields.ManyToManyField(
    #     'models.Robot',
    #     related_name='apis_robots',
    #     through='agentfunctioncallapi_robot',
    #     forward_key="robot_id",
    #     backward_key="agentfunctioncall_id"
    # )
    robots = fields.ManyToManyRelation["Robot"]

    class PydanticMeta:
        exclude = ("updated_at", "deleted_at", "user", "robots_apis")


class AgentFunctionCallApiRobot(models.Model):
    """关系表: 连接AgentFunctionCallApi和Robot, 多对多关系"""

    id = fields.UUIDField(pk=True)
    # agentfunctioncall = fields.ForeignKeyField(
    #     "models.AgentFunctionCallApi",
    #     related_name="agentapis2robotrel",
    #     to_field="id"
    # )
    agentfunctioncall_id = fields.UUIDField()
    # robot = fields.ForeignKeyField(
    #     "models.Robot",
    #     related_name="robot2agentapisrel",
    #     to_field="id"
    # )
    robot_id = fields.UUIDField()
    group_id = fields.UUIDField(null=True)
    related_status = fields.CharField(max_length=50, null=True)

    class Meta:
        table = "agentfunctioncallapi_robot"
        unique_together = (("agentfunctioncall_id", "robot_id"),)


class AgentThoughtMessage(AbstractBaseModel):
    """Agent的思考消息表, 记录Agent的思考过程, 工具调用结果, 模型最终的生辰结果. 主要应用于问题的排查分析"""

    robot_id = fields.UUIDField(null=True)
    session_id = fields.CharField(null=True, max_length=100)
    message_id = fields.UUIDField(null=True)
    user_input = fields.TextField(null=False)
    iteration = fields.SmallIntField(default=0)
    thought_message = fields.TextField(null=True)
    action_name = fields.CharField(max_length=255, null=True)
    action_args = fields.JSONField(null=True)
    is_interface_call = fields.BooleanField(default=False)
    agent_function_call_api_id = fields.UUIDField(null=True)
    agent_function_call_request = fields.JSONField(null=True)
    agent_function_call_return = fields.TextField(null=True)
    agent_function_call_duration = fields.DecimalField(
        null=True, decimal_places=3, max_digits=7
    )
    tool_return = fields.TextField(null=True)
    iteration_duration = fields.DecimalField(null=True, decimal_places=3, max_digits=7)

    class Meta:
        indexes = [("robot_id",), ("session_id",)]


class DurationUnit(str, Enum):
    """有效期的时长单位"""

    DAY = "day"
    # WEEK = "week"
    MONTH = "month"
    # YEAR = "year"


class PlanType(str, Enum):
    TRIAL = "trial"
    BASIC = "basic"


class QuotaGrantType(str, Enum):
    TOTAL = "total"  # 固定总额度 (如 Trial 或 Addon 的一次性额度)
    MONTHLY = "monthly"  # 每月重置额度 (如 Basic Plan 的月度消息数)


class Plan(AbstractBaseModelWithDeletedAt):
    """
    包括基本属性
    - 功能控制：决定功能是否可用（比如：多模态解析功能）
    - 周期性用量限制：比如月度属性额度数量，bot 响应消息数量
    - 固定属性额度数量（比如：bot 数量）
    - 套餐有效期单位
    - 套餐有效期长度
    - 订阅这个套餐的价格
    - 限制性的参数如果不传就表示没有限制
    套餐模型定义：用于定义各种功能的开启状态，以及各种操作的额度
    暂定：接口中不能 修改/删除 这个表的数据，特别是关键字段；因为可能影响已有的订阅
         如果要改，就全新创建一个 plan
    """

    plan_type = fields.CharEnumField(PlanType, default=PlanType.BASIC)  # 新增：套餐类型

    name = fields.CharField(max_length=200, unique=True)  # 套餐名称
    price = fields.DecimalField(
        null=True, max_digits=20, decimal_places=10, validators=[MinValueValidator(0)]
    )  # 费用(单位: 日元)，None 表示"需要询价"

    description = fields.TextField(null=True)  # 套餐说明或功能描述

    # 消耗型额度 (用一次少一次，可能需要周期重置)
    message_quota = fields.IntField(
        null=True, validators=[MinValueValidator(0)]
    )  # 消息额度数量
    message_quota_grant_type = fields.CharEnumField(
        QuotaGrantType, default=QuotaGrantType.MONTHLY
    )  # 消息额度授予类型 (TOTAL / MONTHLY)

    web_page_quota = fields.IntField(
        null=True, validators=[MinValueValidator(0)]
    )  # Web网站导入页数限制
    # web_page_quota_grant_type = fields.CharEnumField(
    #     QuotaGrantType, default=QuotaGrantType.MONTHLY
    # )  # Web网站导入授予类型 (TOTAL / MONTHLY)

    multi_modal_parsing_quota = fields.IntField(
        null=True, validators=[MinValueValidator(0)]
    )  # 多模态解析文件数限制
    # multi_modal_parsing_quota_grant_type = fields.CharEnumField(
    #     QuotaGrantType, default=QuotaGrantType.MONTHLY
    # )  # 多模态解析文件数授予类型 (TOTAL / MONTHLY)

    # 所有套餐的其他用量限制
    storage_quota = fields.BigIntField(
        null=True, validators=[MinValueValidator(0)]
    )  # 存储容量限制 (Byte)
    bot_quota = fields.IntField(
        null=True, validators=[MinValueValidator(0)]
    )  # Bot数量限制

    subscriptions: fields.ReverseRelation["PlanSubscription"]

    # 功能控制，所有注释掉的功能在目前不做，先忽略
    # enable_agent = fields.BooleanField(default=False)  # Agent功能
    # enable_multi_modal_parsing = fields.BooleanField(default=False)  # 多模态解析
    # enable_voice_chat = fields.BooleanField(default=False)  # 实时语音对话
    # enable_large_screen_ui = fields.BooleanField(default=False)  # 大屏操作UI
    # enable_canvas = fields.BooleanField(default=False)  # Canvas功能
    # enable_collaboration = fields.BooleanField(default=False)  # 协同管理
    # enable_widget_customization = fields.BooleanField(default=False)  # Widget定制
    # enable_chat_history_analysis = fields.BooleanField(default=False)  # 聊天历史分析
    # enable_2d_digital_human = fields.BooleanField(default=False)  # 2D数字人

    # LLM模型控制
    # available_llm_models = fields.JSONField(default=list)  # 可用的LLM模型列表

    # @property
    # def is_trial(self) -> bool:
    #     return self.plan_type == PlanType.TRIAL

    class Meta:
        table = "plans"

    class PydanticMeta:
        exclude = ("deleted_at",)


class SubscriptionStatus(str, Enum):
    """Unified status for both Plan and AddOn subscriptions."""

    ACTIVE = "active"  # Subscription is currently valid and usable
    CANCELLED = "cancelled"  # User or admin explicitly cancelled the subscription
    EXPIRED = "expired"  # Subscription period has ended


class PlanSubscription(AbstractBaseModelWithDeletedAt):
    """Model for user subscriptions to specific Plans."""

    user: fields.ForeignKeyRelation["User"] = fields.ForeignKeyField(
        "models.User", related_name="plan_subscriptions"
    )
    plan: fields.ForeignKeyRelation["Plan"] = fields.ForeignKeyField(
        "models.Plan", related_name="subscriptions"
    )

    # 操作状态，表示是否主动取消
    # is_cancelled = fields.BooleanField(default=False)
    cancelled_at = fields.DatetimeField(null=True)

    start_at = fields.DatetimeField()  # Subscription effective time (UTC)
    expires_at = fields.DatetimeField()  # Subscription expiry time (UTC)

    @property
    def status(self) -> SubscriptionStatus:
        """Dynamically computes the subscription status."""
        # Ensure timezone awareness if not already handled
        now = datetime.now(timezone.utc)
        # Make sure start_at and expires_at are timezone-aware for comparison
        start_at_aware = (
            self.start_at.replace(tzinfo=timezone.utc)
            if self.start_at.tzinfo is None
            else self.start_at
        )
        expires_at_aware = (
            self.expires_at.replace(tzinfo=timezone.utc)
            if self.expires_at.tzinfo is None
            else self.expires_at
        )

        if self.is_cancelled:
            return SubscriptionStatus.CANCELLED
        elif now >= expires_at_aware:
            return SubscriptionStatus.EXPIRED
        elif start_at_aware <= now < expires_at_aware:
            return SubscriptionStatus.ACTIVE
        else:
            # should not happen
            logging.error("Invalid subscription status calculation")
            raise ValueError("Invalid subscription status calculation")

    async def cancel(self) -> None:
        """
        取消订阅
        提供明确的方法来修改状态
        """
        self.cancelled_at = datetime.now()
        await self.save(update_fields=["is_cancelled"])

    @classmethod
    async def get_active_subscription(cls, user_id: str) -> List["PlanSubscription"]:
        """
        获取用户的所有活跃订阅
        使用数据库查询优化性能
        """
        now = datetime.now(timezone.utc)
        return await cls.filter(
            user_id=user_id,
            is_cancelled=False,
            start_at__lte=now,
            expires_at__gt=now,
            deleted_at__isnull=True,
        ).all()

    @classmethod
    async def get_expired_subscriptions(cls, user_id: str) -> List["PlanSubscription"]:
        """
        获取用户的所有过期订阅
        """
        now = datetime.now(timezone.utc)
        return await cls.filter(
            user_id=user_id, expires_at__lte=now, deleted_at__isnull=True
        ).all()

    class Meta:
        table = "plan_subscriptions"
        ordering = ["-created_at"]


class AddOnTargetField(str, Enum):
    """增值包可以影响的目标字段"""

    # 数值限制类字段
    MESSAGE_QUOTA = "message_quota"
    BOT_QUOTA = "bot_quota"
    WEB_PAGE_QUOTA = "web_page_quota"
    MULTI_MODAL_PARSING_QUOTA = "multi_modal_parsing_quota"

    # STORAGE_BYTES = "storage_bytes"

    # 功能开关类字段（布尔型）
    # ENABLE_AGENT = "enable_agent"
    # ENABLE_MULTI_MODAL_PARSING = "enable_multi_modal_parsing"
    # ENABLE_VOICE_CHAT = "enable_voice_chat"
    # ENABLE_LARGE_SCREEN_UI = "enable_large_screen_ui"
    # ENABLE_CANVAS = "enable_canvas"
    # ENABLE_COLLABORATION = "enable_collaboration"
    # ENABLE_WIDGET_CUSTOMIZATION = "enable_widget_customization"
    # ENABLE_CHAT_HISTORY_ANALYSIS = "enable_chat_history_analysis"
    # ENABLE_2D_DIGITAL_HUMAN = "enable_2d_digital_human"
    #
    # # 模型列表字段
    # AVAILABLE_LLM_MODELS = "available_llm_models"


class AddOn(AbstractBaseModelWithDeletedAt):
    """
    附加功能模型：用于定义额外购买的功能或容量。
    目标字段决定如何影响 Plan，目标类型决定 effect_data 字段的实际数据类型：
      - 数值型：如 monthly_message_limit 使用整数值；
      - 布尔型：如 enable_2d_digital_human 使用布尔值；
      - 列表型：如 available_llm_models 使用字符串数组。
    """

    name = fields.CharField(max_length=100, unique=True)  # 附加功能名称
    description = fields.TextField(null=True)  # 描述
    price = fields.DecimalField(
        null=True, max_digits=20, decimal_places=10, validators=[MinValueValidator(0)]
    )  # 价格（日元），null 表示需询价

    target_field = fields.CharEnumField(
        AddOnTargetField
    )  # 影响的 Plan 字段（预定义选项）

    # 统一字段：存储增值效果数据，根据 target_field 类型可以是整数、布尔值或数组
    value = fields.JSONField(description="Value of the addon effect (int, bool, list)")

    subscriptions: fields.ReverseRelation["AddOnSubscription"]  # reverse relation

    class Meta:
        table = "add_ons"


class AddOnSubscription(AbstractBaseModelWithDeletedAt):
    """Model for user subscriptions to specific AddOns."""

    user: fields.ForeignKeyRelation["User"] = fields.ForeignKeyField(
        "models.User", related_name="add_on_subscriptions"
    )
    add_on: fields.ForeignKeyRelation["AddOn"] = fields.ForeignKeyField(
        "models.AddOn", related_name="subscriptions"
    )

    # is_cancelled = fields.BooleanField(default=False)
    cancelled_at = fields.DatetimeField(null=True)

    start_at = fields.DatetimeField()  # Subscription effective time (UTC)
    expires_at = fields.DatetimeField()  # Subscription expire time (UTC)

    @property
    def status(self) -> SubscriptionStatus:
        """Dynamically computes the subscription status."""
        # Ensure timezone awareness if not already handled
        now = datetime.now(timezone.utc)
        # Make sure start_at and expires_at are timezone-aware for comparison
        start_at_aware = (
            self.start_at.replace(tzinfo=timezone.utc)
            if self.start_at.tzinfo is None
            else self.start_at
        )
        expires_at_aware = (
            self.expires_at.replace(tzinfo=timezone.utc)
            if self.expires_at.tzinfo is None
            else self.expires_at
        )

        if self.is_cancelled:
            return SubscriptionStatus.CANCELLED
        elif now >= expires_at_aware:
            return SubscriptionStatus.EXPIRED
        elif start_at_aware <= now < expires_at_aware:
            return SubscriptionStatus.ACTIVE
        else:
            # should not happen
            logging.error("Invalid subscription status calculation")
            raise ValueError("Invalid subscription status calculation")

    @staticmethod
    def calculate_expires_at(start_at: datetime, add_on: "AddOn") -> Optional[datetime]:
        """Calculates the expiry time based on start_at and add_on duration."""
        try:
            if add_on.duration_unit == DurationUnit.DAY:
                return start_at + timedelta(days=add_on.duration_length)
            elif add_on.duration_unit == DurationUnit.MONTH:
                return start_at + relativedelta(months=add_on.duration_length)
            # Add other units if necessary
        except Exception as e:
            logging.error(
                f"Error calculating expires_at for AddOn {add_on.id} starting at {start_at}: {e}; {traceback.format_exc()}"
            )
            sentry_sdk_capture_exception(e)
        return None

    async def cancel(self) -> None:
        """
        取消订阅
        提供明确的方法来修改状态
        """
        self.is_cancelled = True
        await self.save(update_fields=["is_cancelled"])

    class Meta:
        table = "add_on_subscriptions"
        ordering = ["-created_at"]


class UsageLog(AbstractBaseModelWithDeletedAt):
    """
    记录用户的各类功能使用情况 (Resource consumption events).
    Used for tracking usage against limits, especially total limits or for auditing.
    'amount' field added to support variable consumption (e.g., storage bytes).
    'source_subscription_id' and 'source_subscription_type' added for precise
    tracking of which subscription quota was consumed.
    """

    user = fields.ForeignKeyField(
        "models.User",
        related_name="usage_logs",
        to_field="id",
        # on_delete=fields.CASCADE # Consider implications
    )
    usage_type = fields.CharEnumField(UsageType)
    amount = fields.BigIntField(
        default=1, description="Amount consumed (e.g., messages=1, storage=bytes)"
    )

    # Added: Track which subscription was debited
    source_subscription_id = fields.UUIDField(
        null=True,
        index=True,
        description="ID of the PlanSubscription or AddOnSubscription debited",
    )
    source_subscription_type = fields.CharField(
        max_length=20, null=True, description="'plan' or 'addon'"
    )

    class Meta:
        # Index for efficient querying by user and type, potentially time range and source
        indexes = [
            ("user", "usage_type", "created_at"),
            (
                "source_subscription_id",
                "source_subscription_type",
                "usage_type",  # Add usage_type for calculating consumed per source/type
                "created_at",  # Add created_at for time-based filtering (monthly reset)
            ),  # Index for calculating usage per source
        ]


class PlanField(str, Enum):
    """Plan fields that can be affected by plans or add-ons"""

    MESSAGE_QUOTA = "message_quota"
    BOT_QUOTA = "bot_quota"
    WEB_PAGE_QUOTA = "web_page_quota"
    MULTI_MODAL_PARSING_QUOTA = "multi_modal_parsing_quota"
    # STORAGE_QUOTA = "storage_quota"


# Use descriptive field names as direct string keys for metadata
PLAN_FIELD_METADATA = {
    PlanField.MESSAGE_QUOTA: {
        "type": "numeric",
        "expected_py_type": int,
        "description": "Message quota (number of messages allowed)",
    },
    PlanField.BOT_QUOTA: {
        "type": "numeric",
        "expected_py_type": int,
        "description": "Bot quota (number of bots allowed)",
    },
    PlanField.WEB_PAGE_QUOTA: {
        "type": "numeric",
        "expected_py_type": int,
        "description": "Web page quota (number of web pages allowed to import)",
    },
    PlanField.MULTI_MODAL_PARSING_QUOTA: {
        "type": "numeric",
        "expected_py_type": int,
        "description": "Multi-modal parsing quota (number of files allowed to parse)",
    },
}
