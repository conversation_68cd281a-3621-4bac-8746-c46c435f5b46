import asyncio
import json
import re
import traceback
import uuid
from datetime import datetime
from io import BytesIO
from uuid import UUID

import dateutil
import pandas as pd
from email_validator import validate_email, EmailNotValidError
from fastapi import APIRouter, Body, Depends, Query
from fastapi.responses import StreamingResponse
from fastapi_pagination import Page
from loguru import logger as logging
from pydantic import Field, create_model
from starlette.status import HTTP_204_NO_CONTENT
from tortoise.expressions import Q
from tortoise.functions import Count
from tortoise.transactions import in_transaction

from mygpt.agent_exp.service.gen_prompt_service import gen_prompt_by_meta_prompt
from mygpt.auth0.init import auth
from mygpt.authorization import (
    current_user,
    verify_admin_access,
    verify_robots_owner,
    verify_super_admin_access,
)
from mygpt.core.vector_storage import VectorStorageFactory
from mygpt.enums import (
    AIConfigType,
    AIModel,
    AIStatus,
    AIType,
    OpenAIModel,
    RobotAccessType,
    VectorFileStatus,
    VectorStorageType,
    ResourceType,
)
from mygpt.error import NotFoundEx<PERSON>, OperationFailedException
from mygpt.file_embeddings import delete_dataset_by_obj, task_delete_attachments
from mygpt.models import (
    AccountMember,
    Dataset,
    Dictionary,
    Faqs,
    Robot,
    RobotAccessStatistics,
    RobotConfig,
    User,
    VectorFile,
    AgentFunctionCallApi,
)
from mygpt.parameters import ListAPIParams, tortoise_paginate
from mygpt.schemata import (
    AIDetailOut,
    AIMemberOut,
    AISimpleOut,
    DictionaryIn,
    DictionaryOut,
    RobotIn,
    RobotOut,
    RobotPageOut,
    RobotPVIn,
    TranslateQuestionIn,
    UpdateRobotIn,
    UserMemberOut,
    GenPromptOut,
    GenPromptIn,
)
from mygpt.services.quota_service import QuotaService
from mygpt.site_crawler import SiteCrawler
from mygpt.utils_stripe import clean_question_cache, get_plan, verify_robots

router = APIRouter(prefix="/robots", tags=["robots"])
ai_router = APIRouter(prefix="/ais", tags=["AIs"])
api_router = APIRouter(prefix="/v1/ais", tags=["GBase AI"])


@router.get(
    "/anonymous",
    response_model=Page[RobotPageOut],
)
async def list_robots(
    ai_type: AIType,
    params: ListAPIParams = Depends(),
):
    if ai_type == AIType.DEMO or ai_type == AIType.PUBLIC:
        queryset = Robot.filter(
            ai_type__in=[AIType.DEMO, AIType.PUBLIC],
            deleted_at__isnull=True,
        ).order_by("-created_at")
    else:
        queryset = Robot.filter(
            ai_type=ai_type,
            deleted_at__isnull=True,
        ).order_by("-created_at")
    queryset = queryset.annotate(
        file_count=Count(
            "vectorfiles", _filter=Q(vectorfiles__deleted_at__isnull=True)
        ),
        faq_count=Count("robot_faqs", _filter=Q(robot_faqs__deleted_at__isnull=True)),
    )
    pages = await tortoise_paginate(queryset, params, ["user", "questions", "datasets"])
    for item in pages.items:
        dataset_ids = [dataset.id for dataset in item.datasets]
        if not dataset_ids:
            item.all_file_count = 0
            item.all_faq_count = 0
        else:
            item.all_file_count = await VectorFile.filter(
                dataset_id__in=[dataset.id for dataset in item.datasets],
                deleted_at__isnull=True,
            ).count()
            item.all_faq_count = await Faqs.filter(
                dataset_id__in=[dataset.id for dataset in item.datasets],
                deleted_at__isnull=True,
            ).count()
    return pages


@router.get(
    "/{ai_id}/anonymous",
    response_model=RobotOut,
)
async def get_ai(
    ai_id: str,
    # user: Auth0User = Security(get_current_user),
    # api_key: ApiKey = Depends(depend_api_key_with_none),
):
    """You can get an AI"""
    # user_id = api_key.user_id if api_key else user.id
    robot_obj = await Robot.get_or_none(id=ai_id, deleted_at__isnull=True).annotate(
        file_count=Count(
            "vectorfiles", _filter=Q(vectorfiles__deleted_at__isnull=True)
        ),
        faq_count=Count("robot_faqs", _filter=Q(robot_faqs__deleted_at__isnull=True)),
    )
    if not robot_obj:
        raise NotFoundException("AI not exist.")
    return await RobotOut.from_tortoise_orm(robot_obj)


@router.get(
    "/{ai_id}",
    response_model=AIDetailOut,
    # dependencies=[
    #     Depends(auth.implicit_scheme),
    #     Depends(verify_admin_access),
    # ],
)
@api_router.get(
    "/{ai_id}",
    summary="Get AI",
    response_model=AIDetailOut,
    # dependencies=[
    #     Depends(auth.implicit_scheme),
    #     Depends(verify_admin_access),
    # ],
)
async def get_ai(
    ai_id: UUID,
):
    """You can get an AI Info"""
    robot_obj = (
        await Robot.get_or_none(id=ai_id, deleted_at__isnull=True)
        .prefetch_related("robotconfigs")
        .annotate(
            file_count=Count(
                "vectorfiles", _filter=Q(vectorfiles__deleted_at__isnull=True)
            ),
        )
    )
    if not robot_obj:
        raise NotFoundException("AI not exist.")
    if robot_obj.ai_status == AIStatus.INIT:
        process_count = await VectorFile.filter(
            robot_id=ai_id,
            file_status__in=[VectorFileStatus.PROCESS, VectorFileStatus.READY],
        ).count()
        if robot_obj.file_count > 0 and process_count == 0:
            await Robot.update_ai_status(ai_id, AIStatus.READY)
    # if not robot_obj.subject_name:
    #     robot_obj.subject_name = robot_obj.name
    return await AIDetailOut.from_tortoise_orm(robot_obj)


@ai_router.post(
    "",
    response_model=RobotOut,
    dependencies=[Depends(auth.implicit_scheme), Depends(verify_robots)],
)
async def create_ai(
    robot: RobotIn,
    user: User = Depends(current_user),
    spa: bool = False,
):
    """You can create a new AI type through this interface"""

    # Check if user can create a new bot based on their quota
    await QuotaService.check_bot_quota(user.id)

    if robot.dataset_ids and not robot.name:
        raise OperationFailedException("Robot name is required.")
    elif not robot.dataset_ids and robot.ai_model == AIModel.WEBSITE:
        if not robot.url and not robot.name:
            raise OperationFailedException("Robot url or Robot name is required.")
    elif robot.ai_model == AIModel.FILE and not robot.name:
        raise OperationFailedException("Robot name is required.")
    if not robot.discrible or robot.discrible.strip() == "":
        raise OperationFailedException("Robot discrible is required.")
    if not robot.name and robot.url:
        site_crawler = SiteCrawler(robot.url, spa)
        response = await site_crawler.parse_base()
        metadata = response.metadata
        robot.name = metadata.get("title")
        robot.discrible = metadata.get("description")
    if robot.embedding_model_name:
        try:
            OpenAIModel(robot.embedding_model_name)
        except Exception as e:
            raise OperationFailedException(
                f"Invalid embedding_model_name {robot.embedding_model_name}."
            )
    else:
        robot.embedding_model_name = ""

    robot.name = robot.name[:100]

    # 支持传入robot_id，如果存在则更新，否则创建新机器人
    # robot的id存在，但是不在当前用户下，还会进行插入操作，然后失败
    robot_obj = None
    if robot.id:
        robot_obj = await Robot.get_or_none(
            id=robot.id,
            user_id=user.user_id,
            deleted_at__isnull=True,
        )

    async with in_transaction("default") as conn:
        if robot_obj:
            robot_obj.name = robot.name
            robot_obj.discrible = robot.discrible
            robot_obj.ai_model = robot.ai_model
            robot_obj.robot_type = robot.robot_type
            if robot.prompt:
                robot_obj.prompt = robot.prompt
            await robot_obj.save(using_db=conn)
        else:
            robot_id = robot.id if robot.id else uuid.uuid4()
            robot_obj = await Robot.create(
                using_db=conn,
                id=robot_id,
                user_id=user.user_id,
                ai_status=AIStatus.INIT,
                name=robot.name,
                discrible=robot.discrible,
                ai_model=robot.ai_model,
                robot_type=robot.robot_type,
                prompt=robot.prompt,
            )

        embedding_model_name = robot.embedding_model_name
        metadata = {
            AIConfigType.VECTOR_STORAGE: VectorStorageType.QDRANT_ONE_COLLECTION,
            AIConfigType.FAQ_VECTOR_STORAGE: VectorStorageType.QDRANT_ONE_COLLECTION,
            AIConfigType.EMBEDDING_MODEL_NAME: embedding_model_name,
        }
        if robot.embedding_dimensions:
            metadata[AIConfigType.EMBEDDING_DIMENSIONS] = robot.embedding_dimensions
        await RobotConfig.set_configs(
            str(robot_obj.id),
            metadata,
        )
        dataset_metadata = {k.value: v for k, v in metadata.items()}
        dataset_obj = None
        if robot.dataset_ids:
            # 一次性获取已经与该机器人关联的所有数据集ID
            existing_dataset_ids = [
                str(ds.id)
                for ds in await Dataset.filter(
                    robots__id=robot_obj.id, deleted_at__isnull=True
                )
            ]
            for dataset_id in robot.dataset_ids:
                dataset_obj = await Dataset.get_or_none(
                    id=dataset_id,
                    user_id=user.user_id,
                    deleted_at__isnull=True,
                )
                if not dataset_obj:
                    raise OperationFailedException("Invalid dataset id.")
                if (
                    dataset_obj.metadata.get(AIConfigType.VECTOR_STORAGE)
                    != VectorStorageType.QDRANT_ONE_COLLECTION.value
                ):
                    raise OperationFailedException("Not allowed to use this dataset.")
                if (
                    dataset_obj.metadata.get(AIConfigType.EMBEDDING_MODEL_NAME)
                    != embedding_model_name
                ):
                    raise OperationFailedException("Not allowed to use this dataset.")
                source_lang = dataset_obj.metadata.get("source_lang")
                if source_lang:
                    await RobotConfig.set_configs(
                        str(robot_obj.id),
                        {"source_lang": source_lang},
                    )
                # 只有当数据集尚未与机器人关联时才添加关联
                if dataset_id not in existing_dataset_ids:
                    await dataset_obj.robots.add(robot_obj)
        if not dataset_obj and robot.init_default_datasets:
            # 检查是否有默认数据集，如果没有默认数据集，并且要求要初始化，则创建一个默认数据集
            existing_dataset = await Dataset.get_or_none(
                robots__id=robot_obj.id,
                user_id=user.user_id,
                deleted_at__isnull=True,
            )
            if not existing_dataset:
                dataset_id = uuid.uuid4()
                dataset_obj = await Dataset.create(
                    using_db=conn,
                    id=dataset_id,
                    name=f"{robot_obj.name}'s dataset",
                    description=robot_obj.discrible,
                    user_id=user.user_id,
                    metadata=dataset_metadata,
                    # 用于向量数据库collection_name，以及opensearch的index id
                    collection_name=str(dataset_id),
                )
                await dataset_obj.robots.add(robot_obj)
        if robot.agent_function_call_ids:
            agent_functioncalls = await AgentFunctionCallApi.filter(
                id__in=robot.agent_function_call_ids
            ).all()
            # 验证agent_function_call_id的有效性
            if len(agent_functioncalls) != len(robot.agent_function_call_ids):
                raise OperationFailedException("Invalid agent_function_call_id.")
            # 验证agent_function_call_id是否属于当前用户
            for agent_functioncall in agent_functioncalls:
                if agent_functioncall.user_id != user.user_id:
                    raise OperationFailedException("Invalid agent_function_call_id.")
            # 根据id查询agent_function_call的信息 todo - 暂时关闭
            # agent_function_call_api_robots = [
            #     AgentFunctionCallApiRobot(
            #         agentfunctioncall=agent_functionall,
            #         robot=robot_obj,
            #         group_id=None,
            #         related_status=None
            #     ) for agent_functionall in agent_functioncalls
            # ]
            # res = await AgentFunctionCallApiRobot.bulk_create(agent_function_call_api_robots)
            # logging.info(f"User: {user.id} create agent_function_call_api_robots: {res}")

    robot_obj = await Robot.get_or_none(id=robot_obj.id).annotate(
        file_count=Count(
            "vectorfiles", _filter=Q(vectorfiles__deleted_at__isnull=True)
        ),
        faq_count=Count("robot_faqs", _filter=Q(robot_faqs__deleted_at__isnull=True)),
    )
    return await RobotOut.from_tortoise_orm(robot_obj)


@router.delete(
    "/{ai_id}",
    dependencies=[Depends(auth.implicit_scheme), Depends(verify_robots_owner)],
    status_code=HTTP_204_NO_CONTENT,
)
@api_router.delete(
    "/{ai_id}",
    summary="Delete AI",
    status_code=HTTP_204_NO_CONTENT,
)
async def delete_ai(
    ai_id: str, delete_dataset: bool = False, user: User = Depends(current_user)
):
    """You can delete an AI type you don't want through this interface."""
    robot_obj = await Robot.get_or_none(
        id=ai_id,
        deleted_at__isnull=True,
    ).prefetch_related("datasets")
    if not robot_obj:
        raise NotFoundException("Invalid AI id.")
    if not delete_dataset:
        for dataset in robot_obj.datasets:
            await dataset.robots.remove(robot_obj)
    else:
        for dataset in robot_obj.datasets:
            dataset_obj = await Dataset.get(id=dataset.id).prefetch_related("robots")
            if len(dataset_obj.robots) > 1:
                raise OperationFailedException(
                    "Dataset has other AI. Don't allow to delete."
                )
        for dataset in robot_obj.datasets:
            # delete dataset
            asyncio.create_task(delete_dataset_by_obj(dataset))
            asyncio.create_task(task_delete_attachments(dataset))
            await dataset.soft_delete()
        asyncio.create_task(task_delete_attachments(robot_obj=robot_obj))
    # 清理bot权限的分享
    members = await AccountMember.filter(resource_id=ai_id, deleted_at__isnull=True)
    for m in members:
        await m.soft_delete()
    await robot_obj.soft_delete()


@router.patch(
    "/{ai_id}",
    response_model=RobotOut,
    dependencies=[Depends(auth.implicit_scheme), Depends(verify_admin_access)],
)
@api_router.patch(
    "/{ai_id}",
    summary="Update AI",
    response_model=RobotOut,
)
async def update_ai(
    ai_id: str,
    robot: UpdateRobotIn,
):
    """
    You can modify your AI type through this interface.
    """
    robot_obj = (
        await Robot.get_or_none(
            id=ai_id,
            deleted_at__isnull=True,
        )
        .prefetch_related("vectorfiles", "datasets")
        .annotate(
            file_count=Count(
                "vectorfiles", _filter=Q(vectorfiles__deleted_at__isnull=True)
            ),
            faq_count=Count(
                "robot_faqs", _filter=Q(robot_faqs__deleted_at__isnull=True)
            ),
        )
    )
    if not robot_obj:
        raise NotFoundException("Invalid AI id.")
    async with in_transaction("default") as conn:
        robot_obj.name = robot.name
        if robot.discrible:
            robot_obj.discrible = robot.discrible
        if robot.ai_type:
            robot_obj.ai_type = robot.ai_type

        if robot.embedding_model_name:
            # 创建 robot 的流程并不是使用单一的创建接口完成, 目前的流程是先创建 robot, 然后再设置配置, 所以这里需要判断是否有 embedding_model_name
            try:
                OpenAIModel(robot.embedding_model_name)
            except Exception as e:
                raise OperationFailedException(
                    f"Invalid embedding_model_name {robot.embedding_model_name}."
                )
            embedding_model_name = robot.embedding_model_name
            metadata = {
                AIConfigType.VECTOR_STORAGE: VectorStorageType.QDRANT_ONE_COLLECTION,
                AIConfigType.FAQ_VECTOR_STORAGE: VectorStorageType.QDRANT_ONE_COLLECTION,
                AIConfigType.EMBEDDING_MODEL_NAME: embedding_model_name,
            }
            dataset_metadata = {k.value: v for k, v in metadata.items()}
            if robot.embedding_dimensions:
                metadata[AIConfigType.EMBEDDING_DIMENSIONS] = robot.embedding_dimensions
            await RobotConfig.set_configs(
                str(robot_obj.id),
                metadata,
            )
            for dataset in robot_obj.datasets.related_objects:
                dataset.metadata = dataset_metadata
                res = await dataset.save()
                print(res)
        else:
            robot.embedding_model_name = ""
        if robot.subject_name and len(robot.subject_name) > 100:
            raise OperationFailedException("Subject name is too long.")
        # 更新时允许更新subject_name为空
        robot_obj.subject_name = robot.subject_name
        if robot.prompt:
            robot_obj.prompt = robot.prompt
        if robot.robot_type:
            robot_obj.robot_type = robot.robot_type
        if robot.ai_model:
            robot_obj.ai_model = robot.ai_model
        if robot.embedding_model_name:
            robot_obj.embedding_model_name = robot.embedding_model_name
        await robot_obj.save()

    return await RobotOut.from_tortoise_orm(robot_obj)


@router.post(
    "/{ai_id}/translate_question",
    dependencies=[
        Depends(auth.implicit_scheme),
        Depends(verify_admin_access),
    ],
    status_code=HTTP_204_NO_CONTENT,
)
async def set_translate_question(
    ai_id: str,
    req: TranslateQuestionIn,
):
    await RobotConfig.update_or_create(
        robot_id=ai_id,
        key=AIConfigType.SOURCE_LANG,
        defaults={"value": req.source_lang},
    )
    await RobotConfig.update_or_create(
        robot_id=ai_id,
        key=AIConfigType.TRANSLATE_QUESTION,
        defaults={"value": req.translate_question},
    )


def is_valid_url(url):
    pattern = re.compile(r"^(?:https?://)?(?:[\w-]+\.)+[a-zA-Z]{2,}(?:/[\w\-.]*)*$")
    return bool(pattern.match(url))


from typing import Optional, Any, List

config_type_mapping = {
    AIConfigType.SOURCE_LANG: (str, "Source language"),
    AIConfigType.TRANSLATE_QUESTION: (bool, "Whether to translate questions"),
    AIConfigType.FALLBACK_TO_CHATGPT: (
        bool,
        "Fallback to ChatGPT when insufficient knowledge",
    ),
    AIConfigType.FRENQUENT_QUESTION: (Any, "Frequent questions"),
    AIConfigType.WIDGET_CONFIGS: (Any, "Widget configurations"),
    AIConfigType.MAX_QUESTIONS: (Any, "Maximum number of questions"),
    AIConfigType.QUESTIONS_START_DATE: (str, "Start date for questions"),
    AIConfigType.QUESTIONS_END_DATE: (str, "End date for questions"),
    AIConfigType.WEB_SEARCH_SITE_LIST: (List[str], "List of web search sites"),
    AIConfigType.CHAT_TEMPREATURE: (Any, "Chat temperature (0-1)"),
    AIConfigType.UNKNOWN_TEXT: (str, "Unknown question answer preset text"),
    AIConfigType.TRANSFER_TO_HUMAN: (Any, "Transfer to human settings"),
    AIConfigType.DIGITAL_HUMAN_PROMPT: (str, "Digital human prompt"),
    AIConfigType.ENABLE_IMAGES: (bool, "Digital human prompt"),
    AIConfigType.ENABLE_ACCESS_CONTROL: (bool, "Enable access control"),
    AIConfigType.ACCESS_CONTROL_ALLOW_EMAIL_LIST: (
        List[str],
        "Access control allow email list",
    ),
}

# Add default configurations for all other AIConfigType members
for config_type in AIConfigType:
    if config_type not in config_type_mapping:
        config_type_mapping[config_type] = (
            Any,
            f"Configuration for {config_type.value}",
        )

fields = {
    config_type.value: (
        Optional[config_type_mapping[config_type][0]],
        Field(None, description=config_type_mapping[config_type][1]),
    )
    for config_type in AIConfigType
}

AIConfigUpdate = create_model(
    "AIConfigUpdate", **fields, __config__=type("Config", (), {"extra": "forbid"})
)


@router.post(
    "/{ai_id}/configs",
    dependencies=[Depends(auth.implicit_scheme), Depends(verify_admin_access)],
    status_code=HTTP_204_NO_CONTENT,
)
async def update_robot_configs(
    ai_id: str,
    config_update: AIConfigUpdate,
):
    delete_keys = []
    updates = {k: v for k, v in config_update.dict().items() if v is not None}
    for key, value in updates.items():
        if key not in [k.value for k in AIConfigType]:
            raise OperationFailedException(f"Invalid config key {key}.")
        if key == AIConfigType.MAX_QUESTIONS:
            err_msg = f"Invalid max questions value {value}. expect null or an int ge 0; null for unlimited quota, 0 for no quota"
            if value is None:
                # no limit
                continue
            # should be an int ge 0
            # 0 means no quota
            elif not isinstance(value, int):
                raise OperationFailedException(err_msg)
            elif value < 0:
                raise OperationFailedException(err_msg)
            # need_clean_cache = True
            # if not isinstance(value, int):
            #     delete_keys.append(key)
            #     continue
            # if value < 0:
            #     raise OperationFailedException(f"Invalid max questions value {value}.")
            # user_obj = await User.get_or_none(robots__id=ai_id).prefetch_related(
            #     "userconfigs"
            # )
            # package = await get_plan(user_obj)
            # # 余额
            # balance = package.max_questions - package.used_questions
            # if value > balance:
            #     raise OperationFailedException(f"Invalid max questions value {value}.")
        elif (
            key == AIConfigType.QUESTIONS_START_DATE
            or key == AIConfigType.QUESTIONS_END_DATE
        ):
            need_clean_cache = True
            if not value:
                delete_keys.append(key)
                continue
            # 日期格式校验
            try:
                dateutil.parser.parse(value)
            except ValueError:
                raise OperationFailedException(f"Invalid config value {value}.")
        elif key == AIConfigType.WEB_SEARCH_SITE_LIST:
            # list_value = json.loads(value)
            if len(value) >= 2:
                raise OperationFailedException(
                    f"Invalid config value {value}. length of {AIConfigType.WEB_SEARCH_SITE_LIST} must be <= 1"
                )
            for url in value:
                if not is_valid_url(url):
                    raise OperationFailedException(
                        f"Invalid URL {url}. URL must be in the format like 'a.com', 'http://b.com', or 'https://x.com/y/z'"
                    )
            updates[key] = json.dumps(value)
        elif key == AIConfigType.CHAT_TEMPREATURE:
            if not isinstance(value, (float, int)) or not 0 <= float(value) <= 1:
                raise OperationFailedException(
                    f"Invalid chat_temperature value {value}. chat_temperature must be in the range of 0-1"
                )
        elif key == AIConfigType.UNKNOWN_TEXT:
            if len(value) >= 200:
                raise OperationFailedException(f"Unknown text size is too large")
        elif key == AIConfigType.TRANSFER_TO_HUMAN:
            updates[key] = json.dumps(value)
        elif key == AIConfigType.INPUT_METHOD:
            if value not in ["text", "voice"]:
                raise OperationFailedException(f"Unknown input method")
        elif key == AIConfigType.PREVIEW_PANEL_API:
            if len(value) > 0 and not is_valid_url(value):
                raise OperationFailedException(
                    f"Invalid URL {value}. URL must be in the format like 'a.com', 'http://b.com', or 'https://x.com/y/z'"
                )
        elif key == AIConfigType.ACCESS_CONTROL_ALLOW_EMAIL_LIST:
            if not isinstance(value, list):
                raise OperationFailedException(f"Invalid {key} value. Must be a list.")

            normalized_emails = []
            for email in value:
                if not isinstance(email, str):
                    raise OperationFailedException(
                        f"Invalid {key} value. Must be a list of strings."
                    )
                try:
                    email_info = validate_email(email, check_deliverability=False)
                    # 使用规范化的电子邮件地址
                    normalized_emails.append(email_info.normalized)
                except EmailNotValidError as e:
                    # 记录错误并抛出异常
                    logging.error(
                        f"Invalid email address: {email}, {e}, {traceback.format_exc()}"
                    )
                    raise OperationFailedException(f"Invalid email address: {email}")
            # 存储规范化后的电子邮件地址列表
            updates[key] = json.dumps(normalized_emails, ensure_ascii=False)
        elif key == AIConfigType.ENABLE_TTS:
            if not isinstance(value, bool):
                raise OperationFailedException(
                    f"Invalid {key} value. Must be a boolean."
                )
        elif key == AIConfigType.ENABLE_2D_DIGITAL_PERSON:
            if not isinstance(value, bool):
                raise OperationFailedException(
                    f"Invalid {key} value. Must be a boolean."
                )
        await clean_question_cache(ai_id)
    for key, value in updates.items():
        try:
            if key in delete_keys:
                await RobotConfig.filter(
                    robot_id=ai_id,
                    key=key,
                ).delete()
            else:
                await RobotConfig.update_or_create(
                    robot_id=ai_id,
                    key=key,
                    defaults={"value": value},
                )
        except Exception as e:
            logging.error(f"fail to update config:{key} error:{e}")
            raise OperationFailedException(f"fail to update config:{key} error:{e}")


@router.post(
    "/{ai_id}/configs/client_installed",
    status_code=HTTP_204_NO_CONTENT,
)
async def update_robot_configs_client_installed(
    ai_id: str,
    value: bool = Body(..., embed=True),
):
    robot_obj = await Robot.get_or_none(
        id=ai_id,
        deleted_at__isnull=True,
    )
    if not robot_obj:
        raise NotFoundException("Invalid AI id.")

    await RobotConfig.update_or_create(
        robot_id=ai_id,
        key=AIConfigType.CLIENT_INSTALLED,
        defaults={"value": value},
    )


@router.get(
    "",
    response_model=Page[RobotPageOut],
    dependencies=[Depends(auth.implicit_scheme)],
)
@api_router.get(
    "",
    summary="Get AIs",
    response_model=Page[RobotPageOut],
)
async def get_ais(
    user: User = Depends(current_user),
    params: ListAPIParams = Depends(),
):
    """
    You can obtain all AI types you create through this interface.
    """

    queryset = (
        Robot.filter(user_id=user.user_id, deleted_at__isnull=True)
        # .annotate(
        #     file_count=Count(
        #         "vectorfiles", _filter=Q(vectorfiles__deleted_at__isnull=True)
        #     ),
        #     faq_count=Count(
        #         "robot_faqs", _filter=Q(robot_faqs__deleted_at__isnull=True)
        #     ),
        # )
        .order_by("-created_at")
    )
    # pages = await tortoise_paginate(queryset, params, ["user", "questions", "datasets", "user__agent_function_call_api",
    #                                                    "apis", "robot2agentapisrel"])
    # pages = await tortoise_paginate(queryset, params, ["datasets", "apis_robots"])
    pages = await tortoise_paginate(queryset, params, ["datasets", "user"])
    for item in pages.items:
        dataset_ids = [dataset.id for dataset in item.datasets]
        if not dataset_ids:
            item.file_count = 0
            item.faq_count = 0
        else:
            item.file_count = await VectorFile.filter(
                dataset_id__in=[dataset.id for dataset in item.datasets],
                deleted_at__isnull=True,
            ).count()
            item.faq_count = await Faqs.filter(
                dataset_id__in=[dataset.id for dataset in item.datasets],
                deleted_at__isnull=True,
            ).count()
    return pages


@router.post(
    "/{ai_id}/status",
    dependencies=[Depends(auth.implicit_scheme), Depends(verify_admin_access)],
    status_code=HTTP_204_NO_CONTENT,
)
async def update_ai_status(
    ai_id: str,
    status: AIStatus,
):
    await Robot.update_ai_status(ai_id, status)


@router.post(
    "/clean_deleted_collection",
    dependencies=[Depends(auth.implicit_scheme), Depends(verify_super_admin_access)],
)
async def clean_deleted_collection():
    async def do_clean():
        datasets = await Dataset.filter(deleted_at__isnull=False)
        for dataset in datasets:
            if dataset.metadata.get("vector_storage") == "qdrant":
                vector_storage_type = VectorStorageType(
                    dataset.metadata.get("vector_storage", VectorStorageType.PINECONE)
                )
                vector_storage = VectorStorageFactory.get_instance()
                try:
                    await vector_storage.delete_collection(dataset.collection_name)
                except Exception as e:
                    logging.error(
                        f"fail to delete collection:{dataset.collection_name} error:{e}"
                    )
                logging.info(f"delete collection:{dataset.collection_name}")

        robots = await Robot.filter(deleted_at__isnull=False).prefetch_related(
            "robotconfigs"
        )
        for robot in robots:
            if len(robot.robotconfigs) == 0:
                try:
                    vector_storage = VectorStorageFactory.get_instance()
                    await vector_storage.delete_collection(str(robot.id))
                    logging.info(f"delete collection:{robot.id}")
                except Exception as e:
                    logging.warning(f"fail to delete collection:{robot.id} error:{e}")
            else:
                vector_storage_str = robot.get_config(AIConfigType.VECTOR_STORAGE)
                if not vector_storage_str:
                    vector_storage_type = VectorStorageType.QDRANT
                else:
                    vector_storage_type = VectorStorageType(vector_storage_str)
                if vector_storage_type != VectorStorageType.QDRANT_ONE_COLLECTION:
                    try:
                        vector_storage = VectorStorageFactory.get_instance()
                        await vector_storage.delete_collection(str(robot.id))
                        logging.info(f"delete collection:{robot.id}")
                    except Exception as e:
                        logging.warning(
                            f"fail to delete collection:{robot.id} error:{e}"
                        )

    asyncio.create_task(do_clean())


@ai_router.get(
    "/members",
    response_model=Page[AIMemberOut],
    dependencies=[
        Depends(auth.implicit_scheme),
    ],
)
async def get_ais_members(
    params: ListAPIParams = Depends(),
    user: User = Depends(current_user),
):
    queryset = (
        Robot.filter(
            user_id=user.user_id,
            deleted_at__isnull=True,
        )
        .prefetch_related("user")
        .order_by("-created_at")
    )
    pages = await tortoise_paginate(queryset, params, [])
    robots = pages.items
    robot_ids = [robot.id for robot in robots]
    if not robot_ids:
        return pages
    members = (
        await AccountMember.filter(
            resource_type=ResourceType.ROBOT,
            resource_id__in=robot_ids,
            deleted_at__isnull=True,
        )
        .prefetch_related("member")
        .order_by("-created_at")
    )
    members_dict = {}
    for member in members:
        if member.resource_id not in members_dict:
            members_dict[member.resource_id] = [member]
        else:
            members_dict[member.resource_id].append(member)
    ai_members = []
    for robot in robots:
        members = []
        if str(robot.id) in members_dict:
            for member in members_dict[str(robot.id)]:
                members.append(UserMemberOut.from_orm(member))
        ai_members.append(AIMemberOut(ai=AISimpleOut.from_orm(robot), members=members))
    pages.items = ai_members
    return pages


@router.post(
    "/{ai_id}/transfer",
    dependencies=[Depends(auth.implicit_scheme), Depends(verify_super_admin_access)],
)
async def transfer_ai(
    ai_id: str,
    user_id: Optional[str] = Body(None, embed=True),
    email: Optional[str] = Body(None, embed=True),
):
    """
    将bot以及bot下的dataset转移给其他用户
    """
    robot_obj = await Robot.get_or_none(
        id=ai_id,
        deleted_at__isnull=True,
    )
    if not robot_obj:
        raise NotFoundException("Invalid AI id.")
    if user_id:
        user_obj = await User.get_or_none(user_id=user_id)
        if not user_obj:
            raise NotFoundException("Invalid user id.")
    else:
        user_objs = await User.filter(email=email)
        if len(user_objs) == 0:
            raise NotFoundException("Invalid user email.")
        if len(user_objs) > 1:
            user_ids = [user_obj.user_id for user_obj in user_objs]
            raise OperationFailedException(
                f"More than one user found. Please use user_id instead.user_ids:{user_ids}"
            )
        user_id = user_objs[0].user_id
    robot_obj.user_id = user_id

    async with in_transaction("default") as conn:
        robot_obj.updated_at = datetime.now()
        await robot_obj.save(using_db=conn, update_fields=["user_id", "updated_at"])
        datasets = await Dataset.filter(robots__id=ai_id)
        for dataset in datasets:
            link_robots = await Robot.filter(datasets__id=dataset.id)
            if len(link_robots) > 1:
                raise OperationFailedException(
                    f"Dataset:{dataset.id} has other AI. Don't allow to transfer."
                )
            dataset.user_id = user_id
            dataset.updated_at = datetime.now()
            await dataset.save(using_db=conn, update_fields=["user_id", "updated_at"])

    return {"msg": "success"}


@router.post(
    "/{ai_id}/dictionaries",
    dependencies=[Depends(auth.implicit_scheme), Depends(verify_admin_access)],
    response_model=list[DictionaryOut],
)
async def create_dictionary(
    ai_id: str,
    dictionary: list[DictionaryIn],
):
    rs = []
    async with in_transaction("default") as conn:
        for item in dictionary:
            query = Q(source=item.source)
            dict_obj = await Dictionary.filter(Q(robot_id=ai_id) & query).first()
            if dict_obj:
                raise OperationFailedException("The source key already exists")
            dict_obj = await Dictionary.filter(
                robot_id=ai_id, target=item.target
            ).first()
            if dict_obj:
                raise OperationFailedException("The target value already exists")
            dict_obj = await Dictionary.create(
                using_db=conn,
                robot_id=ai_id,
                source=item.source,
                target=item.target,
            )
            rs.append(await DictionaryOut.from_tortoise_orm(dict_obj))
    return rs


@router.post(
    "/{ai_id}/dictionaries/{dictionary_id}",
    dependencies=[Depends(auth.implicit_scheme), Depends(verify_admin_access)],
    response_model=DictionaryOut,
)
async def update_dictionary(
    ai_id: str,
    dictionary_id: str,
    dictionary: DictionaryIn,
):
    dict_obj = await Dictionary.get_or_none(
        id=dictionary_id,
        robot_id=ai_id,
    )
    if not dict_obj:
        raise NotFoundException("Invalid dictionary id.")
    query_1 = Q(robot_id=ai_id) & ~Q(id=dictionary_id)
    query_2 = Q(source=dictionary.source) | Q(target=dictionary.target)

    exist_dict_obj = await Dictionary.filter(query_1 & query_2).first()
    query_2 = Q(source=dictionary.source)
    if exist_dict_obj:
        raise OperationFailedException("The source key already exists")
    query_2 = Q(target=dictionary.target)
    exist_dict_obj = await Dictionary.filter(query_1 & query_2).first()
    if exist_dict_obj:
        raise OperationFailedException("The target value already exists")
    dict_obj.source = dictionary.source
    dict_obj.target = dictionary.target
    dict_obj.updated_at = datetime.now()
    await dict_obj.save()
    return await DictionaryOut.from_tortoise_orm(dict_obj)


@router.get(
    "/{ai_id}/dictionaries",
    dependencies=[Depends(auth.implicit_scheme), Depends(verify_admin_access)],
    response_model=Page[DictionaryOut],
)
async def get_dictionaries(
    ai_id: str,
    search: Optional[str] = None,
    start_date: Optional[str] = Query(
        None, description="ISO 8601 format in UTC. Like: '2024-01-11T07:45:14.968Z'"
    ),
    end_date: Optional[str] = Query(
        None, description="ISO 8601 format in UTC. Like: '2024-01-11T07:45:14.968Z'"
    ),
    params: ListAPIParams = Depends(),
):
    queryset = Dictionary.filter(robot_id=ai_id)
    if search:
        queryset = queryset.filter(Q(source__icontains=search))
    try:
        if start_date:
            start_time = datetime.fromisoformat(start_date.replace("Z", "+00:00"))
            queryset = queryset.filter(updated_at__gte=start_time)
        if end_date:
            # end of the day time
            end_time = datetime.fromisoformat(end_date.replace("Z", "+00:00"))
            queryset = queryset.filter(updated_at__lte=end_time)
    except ValueError:
        raise OperationFailedException(
            "Invalid date format. Please use ISO 8601 format in UTC. Like: '2024-01-11T07:45:14.968Z'"
        )

    return await tortoise_paginate(queryset, params, [])


@router.get(
    "/{ai_id}/dictionaries/download",
    dependencies=[Depends(auth.implicit_scheme), Depends(verify_admin_access)],
)
async def download_dictionaries(
    ai_id: str,
    search: Optional[str] = None,
    start_date: Optional[str] = Query(
        None, description="ISO 8601 format in UTC. Like: '2024-01-11T07:45:14.968Z'"
    ),
    end_date: Optional[str] = Query(
        None, description="ISO 8601 format in UTC. Like: '2024-01-11T07:45:14.968Z'"
    ),
):
    queryset = Dictionary.filter(robot_id=ai_id)
    if search:
        queryset = queryset.filter(Q(source__icontains=search))
    try:
        if start_date:
            # start_date to datatime (ISO 8601 format)
            start_time = datetime.fromisoformat(start_date.replace("Z", "+00:00"))
            queryset = queryset.filter(updated_at__gte=start_time)
        if end_date:
            # end of the day time
            end_time = datetime.fromisoformat(end_date.replace("Z", "+00:00"))
            queryset = queryset.filter(updated_at__lte=end_time)
    except ValueError:
        raise OperationFailedException(
            "Invalid date format. Please use ISO 8601 format in UTC. Like: '2024-01-11T07:45:14.968Z'"
        )
    dicts = await queryset.all().order_by("-updated_at")
    filename = f'Data dictionary_{datetime.now().strftime("%Y%m%d")}.xlsx'

    df = pd.DataFrame(
        [[d.source, d.target, d.updated_at.isoformat()] for d in dicts],
        columns=["source", "target", f"time"],
    )
    buffer = BytesIO()
    with pd.ExcelWriter(buffer) as writer:
        df.to_excel(writer, index=False)
    return StreamingResponse(
        BytesIO(buffer.getvalue()),
        media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        headers={"Content-Disposition": f"attachment; filename={filename}"},
    )


@router.get(
    "/{ai_id}/dictionaries/{dictionary_id}",
    dependencies=[Depends(auth.implicit_scheme), Depends(verify_admin_access)],
    response_model=DictionaryOut,
)
async def get_dictionary(
    ai_id: str,
    dictionary_id: str,
):
    dict_obj = await Dictionary.get_or_none(
        id=dictionary_id,
        robot_id=ai_id,
    )
    if not dict_obj:
        raise NotFoundException("Invalid dictionary id.")
    return await DictionaryOut.from_tortoise_orm(dict_obj)


@router.delete(
    "/{ai_id}/dictionaries/{dictionary_id}",
    dependencies=[Depends(auth.implicit_scheme), Depends(verify_admin_access)],
    status_code=HTTP_204_NO_CONTENT,
)
async def delete_dictionary(
    ai_id: str,
    dictionary_id: str,
):
    dict_obj = await Dictionary.get_or_none(
        id=dictionary_id,
        robot_id=ai_id,
    )
    if not dict_obj:
        raise NotFoundException("Invalid dictionary id.")
    await dict_obj.delete()


@router.delete(
    "/{ai_id}/dictionaries",
    dependencies=[Depends(auth.implicit_scheme), Depends(verify_admin_access)],
    status_code=HTTP_204_NO_CONTENT,
)
async def delete_dictionaries(
    ai_id: str,
    dictionary_ids: list[str] = Body(..., embed=True),
):
    await Dictionary.filter(
        robot_id=ai_id,
        id__in=dictionary_ids,
    ).delete()


# 增加机器人PV上报
@router.post(
    "/{ai_id}/pv",
    status_code=HTTP_204_NO_CONTENT,
)
async def add_pv(
    ai_id: str,
    pv_info: RobotPVIn,
):
    async with in_transaction("default") as conn:
        robot_info = await Robot.get_or_none(id=ai_id, deleted_at__isnull=True)
        if robot_info is None:
            raise NotFoundException("Invalid robot id.")

        today_info = await RobotAccessStatistics.get_or_none(
            robot__id=ai_id,
            date=datetime.now().date(),
            type=RobotAccessType.PV,
            url=pv_info.url,
        )
        if today_info:
            today_info.count += 1
            await today_info.save()
        else:
            await RobotAccessStatistics.create(
                robot_id=ai_id,
                date=datetime.now().date(),
                type=RobotAccessType.PV,
                count=1,
                using_db=conn,
                url=pv_info.url,
            )


@router.get(
    "/{ai_id}/is_collaborator",
    response_model=bool,
    dependencies=[Depends(auth.implicit_scheme)],
    summary="Check Collaborator Status",
    description="Determine if the current user has collaborator permissions for the specified AI robot.",
)
async def is_collaborator(
    ai_id: str,
    user: User = Depends(current_user),
) -> bool:
    """
    Check if the current user has collaborator permissions for the specified robot.
    """
    exists = await AccountMember.filter(
        member_id=user.user_id,
        resource_type=ResourceType.ROBOT,
        deleted_at__isnull=True,
        resource_id=ai_id,
    ).exists()
    return exists


@router.post(
    "/gen_prompt",
    response_model=GenPromptOut,
    summary="generate prompt",
    # dependencies=[Depends(auth.implicit_scheme)],
)
@api_router.post(
    "/gen_prompt",
    response_model=GenPromptOut,
    summary="generate prompt",
    # dependencies=[Depends(auth.implicit_scheme), Depends(verify_robots)],
)
async def gen_agent_prompt(
    gen_prompt_in: GenPromptIn,
    user: User = Depends(current_user),
):
    """
    一键生成机器人的prompt, 使用Meta Prompt调用大模型进行生成
    """
    if not gen_prompt_in.prompt_in:
        raise OperationFailedException("prompt_in is required.")
    resp = await gen_prompt_by_meta_prompt(gen_prompt_in)
    if gen_prompt_in.stream:
        return StreamingResponse(
            resp,
            media_type="text/event-stream",
        )
    else:
        return GenPromptOut(prompt=resp.content)


@router.put(
    "/{ai_id}/gen_prompt",
    response_model=GenPromptOut,
    summary="generate prompt",
    dependencies=[Depends(auth.implicit_scheme)],
)
@api_router.put(
    "/{ai_id}/gen_prompt",
    response_model=GenPromptOut,
    summary="generate prompt",
    dependencies=[Depends(auth.implicit_scheme), Depends(verify_robots)],
)
async def gen_agent_prompt(
    gen_prompt_in: GenPromptIn,
    user: User = Depends(current_user),
):
    """
    更新robot的prompt信息, 暂时不用, 后续看看如何修改. todo -
    """
    if not gen_prompt_in.prompt_in:
        raise OperationFailedException("prompt_in is required.")
    resp = await gen_prompt_by_meta_prompt(gen_prompt_in)
    if gen_prompt_in.stream:
        return StreamingResponse(
            resp,
            media_type="text/event-stream",
        )
    else:
        return GenPromptOut(prompt=resp.content)
