import time
import traceback
import uuid
import re
import asyncio
import json
from typing import List, Optional, Dict, Any
from loguru import logger as logging

from mygpt.agent_functioncall.context_management.context import BaseContext
from mygpt.agent_functioncall.schemas.prompt_schemas import PromptType
from mygpt.agent_functioncall.tool_management.manager import tool
from mygpt.agent_functioncall.types import Result
from mygpt.enums import UserIntent, AIConfigType, MESSAGE_COMES_FROM, OpenAIModel
from mygpt.openai_utils import _keywords_generation_4_turbo, user_intent_detect
from mygpt.opensearch_knowledge import OpenSearchKnowledgeClient
from mygpt.dao.opensearch_dao.knowledge_docs_dao import knowledge_docs_dao
from mygpt.schemata import FinalQuestionParams
from mygpt.service.chat.doc_service import search_question_context
from mygpt.service.chat.faq_service import faq_context_search
from mygpt.service.chat.web_service import async_web_search
from mygpt.service.rag_control import analyze_question, prepare_messages
from mygpt.utils import convert_language_code
from mygpt.dao.postgresql_dao.dataset_dao import dataset_dao
from mygpt.dao.postgresql_dao.vectorfile_dao import vectorfile_dao


class TraditionalRAGTool:
    def __init__(self, context, agent=None):
        self.agent = agent
        self.context: BaseContext = context
        self.dataset_dao = dataset_dao
        self.traditional_rag_tool_calls_num = 0
        self.traditional_rag_tool_args_history = []

    async def _extract_metadata(self, doc_text: str) -> Optional[Dict[str, Any]]:
        """
        从文档文本中提取元数据
        """
        try:
            # 查找Metadata部分
            metadata_match = re.search(
                r"### Metadata\n(.*?)\n\n### Content", doc_text, re.DOTALL
            )
            if not metadata_match:
                return None

            metadata_text = metadata_match.group(1)
            metadata = {}

            # 解析每一行的键值对
            for line in metadata_text.strip().split("\n"):
                if ":" in line:
                    key, value = line.split(":", 1)
                    key = key.strip()
                    value = value.strip()

                    # 处理特殊类型
                    if value.startswith("[") and value.endswith("]"):
                        # 处理列表类型
                        value = [v.strip() for v in value[1:-1].split(",")]
                    elif value.lower() in ["true", "false"]:
                        # 处理布尔类型
                        value = value.lower() == "true"
                    elif value.replace(".", "").isdigit():
                        # 处理数字类型
                        value = float(value) if "." in value else int(value)

                    metadata[key] = value

            return metadata
        except Exception as e:
            print(f"提取元数据时出错: {str(e)}")
            return None

    async def _extract_content(self, doc_text: str) -> Optional[str]:
        """
        从文档文本中提取内容部分
        """
        try:
            # 查找Content部分
            content_match = re.search(
                r"### Content\n(.*?)(?=(?:\n={2,}|$))", doc_text, re.DOTALL
            )
            if not content_match:
                return None
            content = content_match.group(1).strip()
            return content
        except Exception as e:
            print(f"提取内容时出错: {str(e)}")
            return None

    async def _extract_document_info(self, text: str):
        # 通过 "DOCUMENT * BEGIN" 分割多个文档
        documents = []
        # 分割多个文档
        doc_parts = text.split("================== DOCUMENT")
        for doc_part in doc_parts:
            if not doc_part.strip() or "BEGIN" not in doc_part:
                continue
            metadata = await self._extract_metadata(doc_part)
            content = await self._extract_content(doc_part)
            if metadata and content:
                documents.append({"metadata": metadata, "content": content})
        return {"documents": documents, "total_documents": len(documents)}

    @tool(
        name="traditional_rag_tool",
        description="""多源检索增强工具，能够根据用户问题从知识库、FAQ和网络搜索中检索相关信息。该工具会分析用户意图，执行向量检索，并整合多种来源的信息，以提供更准确、更全面的回答基础。
        - 工具会返回结构化的文档内容，包含各文档的元数据和正文内容。
        - 如果工具调用成功, 会直接返回召回的文档chunk信息, 包括元信息, 以json格式呈现. 
        - 如果没有召回的文档, 则会提供固定answer: `RAG工具调用成功: No document found.`
        - 如果工具内部运行错误会返回错误信息, 例如: `RAG工具调用失败: {error}` 这种情况是工具内部问题, 需要联系开发者进行排查.
        【警告】如果召回文档成功但内容与用户问题无关，必须直接告知用户无法找到相关信息，禁止再次调用本工具。""",
        params={
            "query": {
                "description": "可以是Agent智能体优化润色过的查询，不必是用户的原始问题。通过提供经过优化的查询语句，能够显著提高检索结果的相关性和准确度。智能体应根据对话历史和当前上下文生成最佳查询表达。",
                "required": False,
                "type": "string"
            }
        },
        return_direct=False
    )
    async def traditional_rag_tool(self, query: str = None) -> Result:
        start_time = time.time()
        logging.info(f"【traditional_rag_tool】query: {query}")
        if not query:
            if self.context and self.context.question_in:
                query = self.context.question_in.question
            else:
                query = self.agent.question_in.question
        if self.traditional_rag_tool_args_history and self.traditional_rag_tool_args_history[-1] == query:
            return Result(
                value="RAG工具调用被系统拒绝: 该问题与上一个问题相同, 请直接告知用户无法找到相关信息。"
            )
        self.traditional_rag_tool_args_history.append(query)
        self.traditional_rag_tool_calls_num += 1
        try:
            # 1. 解析用户意图, 使用的语言, 用于向量检索的关键词
            ai_obj = self.context.robot
            question_in = self.context.question_in
            chat_history_str = self.context.chat_history_str
            ai_lang = ai_obj.get_config(AIConfigType.SOURCE_LANG)
            question_record_obj = self.context.question_record_obj
            chat_history = self.context.messages
            conversation_count = len(chat_history)
            if not ai_lang:
                ai_lang = "en"
            intent_resp, total_tokens = await user_intent_detect(
                session_id="",
                question=query,
                dictionaries=ai_obj.dictionaries(query),
                ai_language=ai_lang
            )
            if not intent_resp:
                user_intent = UserIntent.ASK_INFO_ABOUT_BOT
                logging.warning(f"【traditional_rag_tool】intent_resp is None")
                question_language = "en"
                query_key_words = query
            else:
                user_intent = intent_resp.get("user_intent", None)
                question_language = intent_resp.get("question_language", None)
                query_key_words = intent_resp.get("query_key_words", None)
            response_language = convert_language_code(question_language)
            logging.info(
                f"【traditional_rag_tool】user_intent: {user_intent}, response_language: {response_language}, query_key_words: {query_key_words}"
            )
            # 2.1 文档向量检索
            logging.info(f"【traditional_rag_tool】doc vector search start")
            coroutine_datasets_search = search_question_context(
                ai_obj, query, response_language, query_key_words=query_key_words, dataset_objs=self.context.dataset_objs
            )
            # 2.2 FAQ向量检索
            logging.info(f"【traditional_rag_tool】faq vector search start")
            coroutine_faq_search = faq_context_search(
                ai_id=str(ai_obj.id),
                response_language=response_language,
                question=query,
                query_key_words_in_ai_language=query_key_words,
            )
            # 2.3 WEB内容检索
            logging.info(f"【traditional_rag_tool】web search start")
            coroutine_web_search = async_web_search(
                ai_obj=ai_obj, query_key_words=query_key_words, question=query
            )
            # 2.4 历史记录检索
            # logging.info(f"【answer_rag_bot】chat history search start")
            # coroutine_chat_history = get_chat_history_turbo(
            #     question_in.session_id, FINAL_QUESTION_MAX_CONVERSATION_COUNT
            # )
            # 3. 并发执行以上任务
            logging.info(f"【traditional_rag_tool】start asyncio.gather")
            tasks = [
                coroutine_datasets_search,
                coroutine_web_search,
                coroutine_faq_search
            ]
            tasks_results = await asyncio.gather(*tasks)
            (embeddings, web_search_results, faq_results) = (
                tasks_results
            )
            (
                rs_type,
                faq_obj_list,
                property_info_list,
                new_query,
                original_faq_obj_list,
                opensearch_faq_obj_list,
            ) = faq_results
            # rs_type是字符串 [relate, no_faqs] 如果值为no_faqs, 说明数据库中没有问答对, 不需要走faq的逻辑
            reference_list = []
            answer_faq_ids = []
            if rs_type == "relate" and faq_obj_list:
                # 获取匹配的键值对
                matched_pairs = []
                citations = []
                for faq_obj in faq_obj_list:
                    faq_id = str(faq_obj.id)
                    citation = faq_obj.citation
                    if citation:
                        citations.append(citation)
                    answer_faq_ids.append(faq_id)
                    matched_pairs.append(
                        {
                            "faq_id": faq_id,
                            "question": faq_obj.question,
                            "answer": faq_obj.answer,
                        }
                    )
                # 统计token, 构建reference列表, 收集匹配的answer_faq_id
                question_record_obj.reference_list = reference_list
                question_record_obj.total_tokens = total_tokens
                question_record_obj.comes_from = MESSAGE_COMES_FROM.AGENT_FAQ.value
                # 将问答对儿信息整理成为字符串, 作为工具的最终答案返回
                result_json = {
                    "original_question": question_in.question,
                    "faq_results": matched_pairs,
                    "user_language": response_language,
                }
                result = Result(
                    value=json.dumps(result_json, ensure_ascii=False, indent=4)
                )
                return result
            embeddings.extend(web_search_results)
            embeddings.sort(key=lambda x: x.score, reverse=True)
            question_record_obj.comes_from = MESSAGE_COMES_FROM.AGENT_FAQ.value
            # ---------------------------------------------------------------------
            if not question_in.message_id:
                question_in.message_id = uuid.uuid4()
            question_record_obj.question_metadata = {
                "message_id": str(question_in.message_id),
                "user_intent": user_intent,
                "response_language": response_language,
                "query_key_words": query_key_words,
            }
            # 3. 整理召回的文档信息
            if question_in.with_images is None:
                question_in.with_images = (
                    ai_obj.get_config(AIConfigType.ENABLE_IMAGES).lower() == "true"
                )
            if self.context.agent.prompt_type == PromptType.TTS:
                question_in.with_images = False
            final_chat_question_embeddings = list()
            if (
                ai_obj.get_config(AIConfigType.RECOMMEND).lower() == ""
                or ai_obj.get_config(AIConfigType.RECOMMEND).lower() == "false"
            ):
                final_chat_question_embeddings = embeddings
            elif (
                ai_obj.get_config(AIConfigType.FAQ_SEARCH_ANSWER_WITH_EMBEDDING).lower()
                == "true"
            ):
                final_chat_question_embeddings = [
                    e for e in embeddings if "faq_id" in e.metadata
                ]
            # 3.1 通过final_chat_question_embeddings获取file的一些信息
            file_ids = set(embedding.metadata.get("file_id") for embedding in final_chat_question_embeddings)
            files = await vectorfile_dao.find_files_by_ids(file_ids)
            file_dic = {str(file.id): file for file in files}
            # 3.2 重新赋值title
            for embedding in final_chat_question_embeddings:
                file_id = embedding.metadata.get("file_id")
                if file_id and file_id in file_dic:
                    file_title = file_dic[file_id].title
                    if file_title.startswith("user_define_title: "):
                        file_title = file_title[len("user_define_title: "):]
                        embedding.metadata["title"] = file_title
            # 4. 整理最终的question参数对象
            openai_model = self.context.base_model
            logging.info(f"bot: {ai_obj.id}, final openai_model: {openai_model}")
            fallback_to_chatgpt = (
                ai_obj.get_config(AIConfigType.FALLBACK_TO_CHATGPT).lower() == "true"
            )
            llm_provider = ai_obj.get_config(AIConfigType.LLM_PROVIDER)
            talking_style = ai_obj.get_config(AIConfigType.TALKING_STYLE)
            chat_temperature = None
            try:
                chat_temperature = ai_obj.get_config(AIConfigType.CHAT_TEMPREATURE)
            except Exception as e:
                logging.warning(
                    f"fail to get chat_temperature:{chat_temperature}, error: {e}"
                )
            unknown_text = ai_obj.get_config(AIConfigType.UNKNOWN_TEXT)
            params = FinalQuestionParams(
                bot_name=ai_obj.name,
                display_subject_name=ai_obj.display_subject_name,
                unknown_text=unknown_text,
                response_language=response_language,
                chat_history=chat_history,
                openai_model=openai_model,
                llm_provider=llm_provider,
                fallback_to_chatgpt=fallback_to_chatgpt,
                dictionaries=ai_obj.dictionaries(question_in.question),
                talking_style=talking_style,
            )
            (
                messages,
                resources,
                reference_list,
                total_tokens,
                openai_model,
            ) = await prepare_messages(
                question_in,
                final_chat_question_embeddings,
                params,
            )
            self.context.agent.context_vars["resources"] = resources
            question_record_obj.reference_list = reference_list
            question_record_obj.total_tokens = total_tokens
            question_record_obj.model = openai_model
            logging.info(
                f"use final_question:{repr(messages[-1].content)} ask ai: {ai_obj.id}, final_question_use_gpt_4: {False}, openai_model: {openai_model} in messages: {repr(messages)}"
            )
            none_content_reference_list = []
            for reference in question_record_obj.reference_list:
                none_content_reference_list.append({**reference, "content": ""})
            self.context.agent.stream_callback.reference_list = none_content_reference_list
            question_record_obj.contexts = [message.content for message in messages]
            question_record_obj.comes_from = MESSAGE_COMES_FROM.CHUNK.value
            if reference_list:
                messages_str = "The RAG tool invocation was successful:\n---\n" + json.dumps(reference_list, ensure_ascii=False, indent=2)
                messages_str += "\n---\n【重要提示】请验证以上内容是否与原始查询相关。如不相关，直接告知用户无法找到相关信息，不要再次调用本工具。"
            # 优化召回的文档信息
            # if len(messages) > 0:
            #     response_dict = await self._extract_document_info(messages[0].content)
            #     if not response_dict.get("documents"):
            #         messages_str = "RAG工具调用成功: No document found."
            #     else:
            #         messages_str = "RAG工具调用成功:\n\n" + json.dumps(response_dict, ensure_ascii=False, indent=2)
            #         messages_str += "\n\n【重要提示】请验证以上内容是否与原始查询相关。如不相关，直接告知用户无法找到相关信息，不要再次调用本工具。"
            else:
                messages_str = "The RAG tool invocation was successful: No document found."
            # 将召回的messages转换为字符串
            # messages_str = "\n".join([message.content for message in messages])
            result = Result(value=messages_str)
            end_time = time.time()
            logging.info(
                f"【{question_in.message_id}】RagTool cost time: {end_time - start_time} seconds"
            )
            return result
        except Exception as e:
            logging.error(f"The invocation of the RAG tool failed: {e}")
            logging.error(traceback.format_exc())
            raise e

