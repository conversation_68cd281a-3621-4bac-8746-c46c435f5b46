import asyncio
import traceback
from typing import Optional
from uuid import uuid4

import httpx
from langchain_community.embeddings import (
    OpenAIEmbeddings,
    AzureOpenAIEmbeddings,
)
from loguru import logger as logging

from mygpt import settings
from mygpt.core.embeddings import OpenAICompatLocalEmbeddings
from mygpt.core.transformer_embeddings import TransformerEmbeddings
from mygpt.core.utils import (
    event_hooks,
    retry_async,
    get_concurrent_provider,
    task_first_completed,
    get_provider,
)
from mygpt.enums import (
    EMBEDDINGS_MODEL,
    OpenAIModel,
    VectorStorageType,
    AIConfigType,
    VectorFileStatus,
    VectorFileType,
    LLM_PROVIDER,
)
from mygpt.file_embeddings import (
    spliter_document,
    create_logged_task,
    convert_word_to_pdf,
)
from mygpt.models import VectorFile
from mygpt.opensearch import get_open_search_client
from mygpt.token_utils import verify_token_dataset, sync_user_token
from mygpt.utils import (
    async_timing_decorator,
    num_tokens_from_string,
    compute_charactors_count,
    get_hash,
)


@async_timing_decorator
async def file_embeddings(
    vector_storage_type: VectorStorageType,
    dataset_id,
    collection_name,
    file_id,
    documents,
    metadata: dict,
    embedding_model_name: OpenAIModel,
    embedding_model: AIConfigType.EMBEDDINGS_MODEL = EMBEDDINGS_MODEL.OPENAI,
    embedding_dimensions: Optional[int] = None,
    is_first: bool = True,
    is_last: bool = True,
    use_page_content_in_doc=False,
):
    from mygpt.core.vector_storage import VectorStorageFactory

    current_vector_file = await VectorFile.get(id=file_id, deleted_at__isnull=True)
    if not current_vector_file:
        logging.warning(f"file_id:{file_id} not found")
        return None

    index_ids = current_vector_file.index_ids or []

    current_metadata = current_vector_file.metadata or {}
    current_index = current_metadata.get("chunk_count", 0)
    current_page_size = current_metadata.get("page_size", 0)

    """
    Split a document into chunks and save to vectorstore
    """
    ids, docs = await spliter_document(
        documents, current_index, use_page_content_in_doc
    )
    if len(docs) == 0:
        logging.warning(
            f"Failed to create document in file_id:{file_id} with error:No documents"
        )

    doc_id = metadata.get("doc_id", uuid4())
    doc_sections = metadata.get("sections", None)
    # compute chunks tokens
    total_tokens = 0
    characters_count = 0
    chunk_count = current_index
    page_size = current_page_size

    for doc in docs:
        if "title" in metadata:
            doc.metadata["title"] = metadata["title"]
        total_tokens += num_tokens_from_string(doc.page_content)
        doc.metadata["file_id"] = file_id
        doc.metadata["doc_id"] = str(doc_id)
        if doc_sections and doc.metadata["page_numbers"]:
            chunk_sections = []
            doc_sections = sorted(doc_sections, key=lambda x: x["start_page_number"])
            for i in range(len(doc_sections) - 1):
                doc_sections[i]["end_page_number"] = (
                    doc_sections[i + 1]["start_page_number"] - 1
                )
            # 最后一个section的end_page_number
            doc_sections[-1]["end_page_number"] = doc_sections[-1]["start_page_number"]
            for page_number in doc.metadata["page_numbers"]:
                for sec in doc_sections:
                    if (
                        page_number <= sec["end_page_number"]
                        and page_number >= sec["start_page_number"]
                    ):
                        chunk_sections.append(sec)
            doc.metadata["sections"] = chunk_sections
        if not doc.metadata.get("short_chunk", False):
            chunk_count += 1
            # 计算字符数
            characters_count += compute_charactors_count(doc.page_content)
        page_numbers = doc.metadata.get("page_numbers", [])
        if page_numbers:
            page_numbers = [(page + current_page_size) for page in page_numbers]
            page_size = max(max(page_numbers), page_size)
            doc.metadata["page_numbers"] = page_numbers

    characters_count += current_vector_file.characters_count
    total_tokens += current_vector_file.tokens()

    # verify tokens
    if dataset_id and not await verify_token_dataset(
        tokens=total_tokens, dataset_id=dataset_id
    ):
        failed_reason = "You do not have enough tokens to pay for this activity."
        await VectorFile.filter(id=file_id).update(
            file_status=VectorFileStatus.Exceeded,
            failed_reason=failed_reason,
        )
        logging.warning(
            f"Failed to create document in file_id:{file_id} with error:{failed_reason}"
        )
        return await VectorFile.get(id=file_id)

    # save to vectorstore
    failed_reason = None
    embeddings = None
    os_client = None
    try:
        group_id = collection_name

        embeddings = EmbeddingFactory.get_instance(
            provider=embedding_model,
            async_mode=False,
            model_name=embedding_model_name,
            dimensions=embedding_dimensions,
        )
        # 添加documents到opensearch库中
        os_client = get_open_search_client()
        if os_client:
            create_logged_task(os_client.add_documents, collection_name, ids, docs)
        docs.page
        await VectorStorageFactory.save_points(
            embeddings=embeddings,
            doc_ids=ids,
            documents=docs,
            group_id=group_id,
            storage_type=vector_storage_type,
        )
        failed_reason = None
        logging.info(f"Success to storage_chunks in file_id:{file_id}")
    except Exception as e:
        traceback.print_exc()
        failed_reason = str(e)
        # all index_ids for file
        index_ids.extend(ids)
        # delete Qdrant chunks
        if embeddings:
            asyncio.create_task(
                VectorStorageFactory.delete_points(
                    embeddings, index_ids, vector_storage_type
                )
            ).add_done_callback(lambda _: logging.info("delete points success"))
        # 删除 opensearch 索引
        if os_client:
            asyncio.create_task(
                asyncio.to_thread(
                    os_client.delete_documents_safe,
                    collection_name,
                    index_ids,
                    [file_id],
                )
            ).add_done_callback(lambda _: logging.info("delete documents success"))
        await asyncio.sleep(1)
    if failed_reason:
        await VectorFile.filter(id=file_id).update(
            file_status=VectorFileStatus.FAIL,
            failed_reason=failed_reason,
        )
        logging.warning(
            f"Failed to create document in file_id:{file_id} with error:{failed_reason}"
        )
        return await VectorFile.get(id=file_id)
    # update vectorfile status,save index_ids,
    metadata["chunk_count"] = chunk_count
    metadata["page_size"] = page_size

    index_ids.extend(ids)

    vector_file = await VectorFile.filter(id=file_id, deleted_at__isnull=True).first()
    if not vector_file:
        logging.warning(f"file_id:{file_id} not found")
        return None
    try:
        if "outlines" in metadata and is_first:
            metadata["outlines"] = [
                dict(outline) for outline in metadata.get("outlines", [])
            ]
        update_data = {
            "index_ids": index_ids,
            "token_count": total_tokens,
            "characters_count": characters_count,
        }
        if is_first:
            update_data["metadata"] = metadata
            #update_data["content_hash"] = get_hash(documents[0].page_content)
        else:
            current_metadata.update(
                **{
                    "chunk_count": chunk_count,
                    "page_size": page_size,
                }
            )
            update_data["metadata"] = current_metadata
        if is_last:
            update_data["file_status"] = VectorFileStatus.COMPLETE
            update_data["failed_reason"] = None
            await sync_user_token(update_data["token_count"], dataset_id)
        await vector_file.update_from_dict(update_data).save()
        if (
            vector_file.file_type == VectorFileType.UPLOAD
            and (vector_file.key.endswith(".docx") or vector_file.key.endswith(".doc"))
            and (
                vector_file.filename.endswith(".docx")
                or vector_file.filename.endswith(".doc")
            )
        ):
            logging.info(
                f"start convert_word_to_pdf file: {vector_file.filename}, id: {vector_file.id}"
            )
            asyncio.create_task(convert_word_to_pdf(vector_file.id))
    except Exception as e:
        logging.error(traceback.format_exc())
        logging.error(f"update vectorfile error:{e}")
        # 删除 opensearch 索引
        if os_client:
            asyncio.create_task(
                asyncio.to_thread(
                    os_client.delete_documents_safe,
                    collection_name,
                    index_ids,
                    [file_id],
                )
            )
        # 删除 Qdrant chunks
        if embeddings:
            asyncio.create_task(
                VectorStorageFactory.delete_points(
                    embeddings, index_ids, vector_storage_type
                )
            )
    return vector_file


class EmbeddingFactory:
    # 模型单例模式
    _instances = {}

    @classmethod
    def get_instance(
        cls,
        provider: LLM_PROVIDER = LLM_PROVIDER.OPENAI,
        async_mode: bool = True,
        model_name: OpenAIModel = OpenAIModel.TEXT_EMBEDDING_ADA_002,
        **kwargs,
    ) -> OpenAIEmbeddings | OpenAICompatLocalEmbeddings:
        key = f"{provider}_{model_name}_{async_mode}"

        if key in cls._instances:
            # 如果已经有实例，直接返回
            return cls._instances[key]
        if settings.DEBUG:
            if async_mode:
                http_client = httpx.AsyncClient(
                    http2=True, verify=False, event_hooks=event_hooks
                )
            else:
                http_client = httpx.Client(
                    http2=True, verify=False, event_hooks=event_hooks
                )
        else:
            # 判断是否为异步模式
            if async_mode:
                http_client = httpx.AsyncClient(http2=True, verify=False)
            else:
                http_client = httpx.Client(http2=True, verify=False)

        if model_name == OpenAIModel.TEXT_EMBEDDING_ADA_002 and "dimensions" in kwargs:
            kwargs.pop("dimensions")
        elif settings.USE_LOCAL_EMBEDDING:
            kwargs.pop("dimensions")
        elif "dimensions" in kwargs and not kwargs["dimensions"]:
            kwargs.pop("dimensions")

        # 兼容本地embedding
        if settings.USE_LOCAL_EMBEDDING:
            # open_ai_key = settings.LOCAL_EMBEDDING_KEY
            # kwargs["openai_api_base"] = settings.LOCAL_EMBEDDING_PROXY
            # cls._instances[key] = OpenAIEmbeddings(
            cls._instances[key] = OpenAICompatLocalEmbeddings(
                # cls._instances[key] = LocalEmbeddings(
                openai_api_base=settings.LOCAL_EMBEDDING_PROXY,
                openai_api_key=settings.LOCAL_EMBEDDING_KEY,
                model=settings.LOCAL_EMBEDDING_MODEL_NAME,
                max_retries=0,
                http_client=http_client,
                timeout=20,
                **kwargs,
            )
        elif provider == LLM_PROVIDER.OPENAI:
            open_ai_key = settings.OPENAI_API_KEY
            cls._instances[key] = OpenAIEmbeddings(
                openai_api_key=open_ai_key,
                model=model_name.value,
                max_retries=0,
                http_client=http_client,
                timeout=30,
                **kwargs,
            )
        elif provider == LLM_PROVIDER.AZURE:
            model_info = settings.AZURE_MODEL.get(model_name)
            cls._instances[key] = AzureOpenAIEmbeddings(
                deployment=model_info.deployment_name,
                openai_api_key=model_info.identity_key,
                azure_endpoint=model_info.endpoints,
                openai_api_version=model_info.version,
                http_client=http_client,
                chunk_size=16,
                max_retries=10,
                timeout=30,
                **kwargs,
            )
        elif provider == LLM_PROVIDER.LOCAL_LLM:
            cls._instances[key] = TransformerEmbeddings(
                url=settings.TRANSFORMERS_URL,
            )
        else:
            raise ValueError(f"Unknown embeddings model: {provider}")
        return cls._instances[key]

    @classmethod
    @retry_async
    async def do_embedding(self, embedding_model: OpenAIEmbeddings, input: list[str]):
        return await embedding_model.aembed_documents(input)

    @classmethod
    async def concurrent_embedding(
        cls,
        input: list[str],
        model_name: OpenAIModel = OpenAIModel.TEXT_EMBEDDING_ADA_002,
        provider: EMBEDDINGS_MODEL = EMBEDDINGS_MODEL.OPENAI,
        dimensions: Optional[int] = None,
    ):
        """
        并发生成embedding，并使用第一个生成的embedding
        """
        if provider == EMBEDDINGS_MODEL.SENTENCE_TRANSFORMERS:
            # 本地模型
            provides = [LLM_PROVIDER.LOCAL_LLM]
        else:
            provides = get_concurrent_provider()
        embeddings = [
            cls.get_instance(
                provide,
                model_name=model_name,
                dimensions=dimensions,
            )
            for provide in provides
        ]
        if len(embeddings) == 1:
            return await cls.do_embedding(embeddings[0], input)
        # 并发请求，使用第一个返回的结果
        tasks = [
            cls.do_embedding(embedding_model, input) for embedding_model in embeddings
        ]
        idx, result = await task_first_completed(*tasks)
        if not result:
            raise ValueError(f"Failed to get embedding from {provides}")
        logging.debug(f"multi use {'openai' if idx == 0 else 'azure'}")
        return result

    @classmethod
    async def embedding(
        cls,
        input: list[str],
        model_name: OpenAIModel = OpenAIModel.TEXT_EMBEDDING_ADA_002,
        provider: EMBEDDINGS_MODEL = EMBEDDINGS_MODEL.OPENAI,
        dimensions: Optional[int] = None,
    ):
        if provider != EMBEDDINGS_MODEL.SENTENCE_TRANSFORMERS:
            provider = get_provider()
        embedding_model = cls.get_instance(
            provider,
            model_name=model_name,
            dimensions=dimensions,
        )
        return await cls.do_embedding(embedding_model, input, max_retries=4)
